import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/auth/data/datasources/auth_data_source.dart';
import 'package:storetrack_app/features/auth/domain/usecases/login_nz_use_case.dart';
import 'package:storetrack_app/features/auth/domain/usecases/reset_pw_nz_use_case.dart';
import 'package:storetrack_app/features/auth/presentation/blocs/reset_password/reset_password_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/dashboard/dashboard_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/schedule/schedule_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_history/store_history_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/today/today_page_cubit.dart';

import '../config/routes/app_router.dart';
import '../core/database/realm_database.dart';
import '../core/network/api_client.dart';
import '../core/network/network_config.dart';
import '../core/network/network_info.dart';
import '../core/services/location_service.dart';
import '../core/services/camera_service.dart';
import '../core/storage/data_manager.dart';
import '../core/storage/storage_service.dart';
import '../core/storage/token_manager.dart';
import '../features/auth/data/repositories/auth_repository.dart';
import '../features/auth/domain/repositories/auth_repository.dart';
import '../features/auth/domain/usecases/login_use_case.dart';
import '../features/auth/presentation/blocs/auth/auth_cubit.dart';
import '../features/home/<USER>/usecases/get_calendar_usecase.dart';
import '../features/home/<USER>/usecases/get_previous_tasks_usecase.dart';
import '../features/home/<USER>/usecases/get_previous_tasks_optimize_usecase.dart';
import '../features/home/<USER>/usecases/submit_report_usecase.dart';
import '../features/notification/data/datasources/notification_datasource.dart';
import '../features/notification/domain/repositories/notification_repository.dart';
import '../features/notification/domain/repositories/notification_repository_impl.dart';
import '../features/notification/domain/usecases/get_notification_usecase.dart';
import '../features/notification/presentation/blocs/notification_cubit.dart';
import '../features/home/<USER>/datasources/home_local_datasource.dart';
import '../features/home/<USER>/datasources/home_remote_datasource.dart';
import '../features/home/<USER>/repositories/home_repository.dart';
import '../features/home/<USER>/repositories/home_repository_impl.dart';
import '../features/home/<USER>/usecases/get_tasks_usecase.dart';
import '../features/home/<USER>/usecases/get_open_count_usecase.dart';
import '../features/home/<USER>/repositories/open_count_repository.dart';
import '../features/home/<USER>/repositories/open_count_repository_impl.dart';
import '../features/home/<USER>/services/photo_service.dart';
import '../features/home/<USER>/blocs/unschedule/unschedule_cubit.dart';

/// GetIt instance
final sl = GetIt.instance;

/// Initializes the dependency injection container by setting up
/// core storage dependencies and services.
Future<void> initLocator() async {
  sl.registerSingleton<AppRouter>(AppRouter());

  try {
    // Initialize core services first
    await _initializeStorage();
    await _initializeNetwork();
    await _initializeRealm();

    // Then initialize features
    await _initializeAuth();
    await _initializeUnscheduleTask();
    await _initializeDashboard();
    await _initializeNotification();
    await _initializeLocationService();
    await _initializeCameraService();
    await _initializePhotoService();
    await _initializeScheduleTask();
  } catch (e) {
    logger("Error initializing service locator: $e");
    // Re-throw to make the error visible
    rethrow;
  }
}

Future<void> _initializeStorage() async {
  /// Initialize SharedPreferences
  final sharedPrefs = await SharedPreferences.getInstance();
  sl
    ..registerSingleton<SharedPreferences>(sharedPrefs)
    ..registerSingleton<StorageService>(
      StorageServiceImpl(
        sl<SharedPreferences>(),
      ),
    )
    ..registerSingleton<DataManager>(
      DataManagerImpl(
        sl<StorageService>(),
      ),
    );
}

// Initialize Realm Database
Future<void> _initializeRealm() async {
  sl.registerSingleton<RealmDatabase>(RealmDatabase.instance);
}

Future<void> _initializeNetwork() async {
  /// Register network dependencies
  final internetConnection = InternetConnection.createInstance();
  sl
    ..registerLazySingleton<InternetConnection>(
      () => internetConnection,
    )
    ..registerLazySingleton<NetworkInfo>(
      () => NetworkInfo(sl<InternetConnection>()),
    )
    ..registerLazySingleton<TokenManager>(
      () => TokenManagerImpl(sl()),
    )
    ..registerLazySingleton<ApiClient>(
      () => ApiClient(
        config: NetworkConfig.production(),
        tokenManager: sl(),
        networkInfo: sl(),
      ),
    )
    ..registerLazySingleton<Dio>(
      () => sl<ApiClient>().instance,
    );
}

Future<void> _initializeAuth() async {
  sl.registerLazySingleton<AuthDataSource>(
      () => AuthDataSourceImpl(networkClient: sl<ApiClient>()));
  sl.registerLazySingleton<AuthRepository>(
      () => AuthRepositoryImpl(dataSource: sl<AuthDataSource>()));
  sl.registerLazySingleton<LoginNZUseCase>(
      () => LoginNZUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton<LoginUseCase>(() => LoginUseCase(
        sl<AuthRepository>(),
      ));
  sl.registerLazySingleton<ResetPwNzUseCase>(() => ResetPwNzUseCase(
        sl<AuthRepository>(),
      ));

  sl.registerLazySingleton<AuthCubit>(() => AuthCubit(
        sl<LoginNZUseCase>(),
        sl<LoginUseCase>(),
      ));
  sl.registerLazySingleton<ResetPasswordCubit>(() => ResetPasswordCubit(
        sl<ResetPwNzUseCase>(),
      ));
}

Future<void> _initializeUnscheduleTask() async {
  sl.registerLazySingleton<HomeRemoteDataSource>(
      () => HomeDataSourceImpl(networkClient: sl<ApiClient>()));
  sl.registerLazySingleton<HomeLocalDataSource>(
      () => HomeLocalDataSourceImpl(realmDatabase: sl<RealmDatabase>()));
  sl.registerLazySingleton<HomeRepository>(() => HomeRepositoryImpl(
        remoteDataSource: sl<HomeRemoteDataSource>(),
        networkInfo: sl<NetworkInfo>(),
        localDataSource: sl<HomeLocalDataSource>(),
      ));
  sl.registerLazySingleton<GetTasksUseCase>(
      () => GetTasksUseCase(sl<HomeRepository>()));
  sl.registerLazySingleton<GetCalendarUseCase>(
      () => GetCalendarUseCase(sl<HomeRepository>()));
  sl.registerLazySingleton<GetPreviousTasksUseCase>(
      () => GetPreviousTasksUseCase(sl<HomeRepository>()));
  sl.registerLazySingleton<GetPreviousTasksOptimizeUseCase>(
      () => GetPreviousTasksOptimizeUseCase(sl<HomeRepository>()));
  sl.registerLazySingleton<SubmitReportUseCase>(
      () => SubmitReportUseCase(sl<HomeRepository>()));
  sl.registerLazySingleton<UnscheduleTaskCubit>(() => UnscheduleTaskCubit(
        sl<GetTasksUseCase>(),
        sl<GetCalendarUseCase>(),
        sl<SubmitReportUseCase>(),
      ));
}

Future<void> _initializeDashboard() async {
  // Register OpenCountRepository
  sl.registerLazySingleton<OpenCountRepository>(() => OpenCountRepositoryImpl(
        remoteDataSource: sl<HomeRemoteDataSource>(),
        networkInfo: sl<NetworkInfo>(),
        localDataSource: sl<HomeLocalDataSource>(),
      ));

  // Register GetOpenCountUseCase
  sl.registerLazySingleton<GetOpenCountUseCase>(
      () => GetOpenCountUseCase(sl<OpenCountRepository>()));

  // Dashboard cubit uses GetTasksUseCase and GetOpenCountUseCase
  sl.registerLazySingleton<DashboardCubit>(() => DashboardCubit(
        sl<GetTasksUseCase>(),
        sl<GetOpenCountUseCase>(),
      ));
}

Future<void> _initializeNotification() async {
  // Data Source
  sl.registerLazySingleton<NotificationRemoteDataSource>(
      () => NotificationRemoteDataSourceImpl(sl<Dio>()));
  // Repository
  sl.registerLazySingleton<NotificationRepository>(
      () => NotificationRepositoryImpl(sl<NotificationRemoteDataSource>()));
  sl.registerLazySingleton(
      () => GetNotificationUseCase(sl<NotificationRepository>()));
  // Cubit
  sl.registerFactory<NotificationCubit>(// Often Factory for Cubits/Blocs
      () => NotificationCubit(sl<GetNotificationUseCase>()));
}

/// Initialize location service
Future<void> _initializeLocationService() async {
  sl.registerLazySingleton<LocationService>(() => LocationServiceImpl());
}

/// Initialize camera service
Future<void> _initializeCameraService() async {
  sl.registerLazySingleton<CameraService>(() => CameraServiceImpl());
}

/// Initialize photo service
Future<void> _initializePhotoService() async {
  sl.registerLazySingleton<PhotoService>(() => PhotoServiceImpl(
        realmDatabase: sl<RealmDatabase>(),
      ));
}

Future<void> _initializeScheduleTask() async {
  // Use the home repository for the schedule cubit
  sl.registerLazySingleton<ScheduleTaskCubit>(() => ScheduleTaskCubit(
        sl<GetTasksUseCase>(),
        sl<GetCalendarUseCase>(),
        sl<SubmitReportUseCase>(),
      ));

  // Register TodayPageCubit
  sl.registerLazySingleton<TodayPageCubit>(() => TodayPageCubit(
        sl<GetTasksUseCase>(),
        sl<GetCalendarUseCase>(),
        sl<SubmitReportUseCase>(),
      ));

  // Register StoreHistoryCubit
  sl.registerFactory<StoreHistoryCubit>(() => StoreHistoryCubit(
        sl<GetPreviousTasksUseCase>(),
        sl<GetPreviousTasksOptimizeUseCase>(),
      ));
}
