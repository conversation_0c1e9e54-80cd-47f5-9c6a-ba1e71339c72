import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart'
    show PhotoUploadWidget;

class DropdownWidget extends StatelessWidget {
  final Measurement measurement;
  final String? value;
  final Function(String?) onChanged;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;
  final String? errorText;
  final List<String> selectedImages;
  final String? photoErrorText;

  const DropdownWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
    this.errorText,
    this.selectedImages = const [],
    this.photoErrorText,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = measurement.measurementOptions ?? [];

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator
          Row(
            children: [
              Expanded(
                child: Text(
                  measurement.measurementDescription ?? 'Select Option',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
            ],
          ),
          const Gap(16),
          DropdownButtonFormField<String>(
            value: value,
            borderRadius: BorderRadius.circular(10),
            dropdownColor: Colors.white,
            decoration: InputDecoration(
              hintText: 'Select...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(
                  color: errorText != null ? Colors.red : AppColors.blackTint2,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(
                  color: errorText != null ? Colors.red : AppColors.blackTint2,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(
                  color: errorText != null ? Colors.red : AppColors.primaryBlue,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 8.0,
              ),
              errorText: errorText,
              errorStyle: textTheme.bodySmall?.copyWith(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
            isExpanded: true,
            items: options.map((option) {
              return DropdownMenuItem<String>(
                value: option.measurementOptionDescription,
                child: Text(
                  option.measurementOptionDescription ?? 'Unnamed Option',
                  style: textTheme.bodyMedium?.copyWith(
                    color: AppColors.black,
                    fontSize: 15,
                  ),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            icon: const Icon(
              Icons.keyboard_arrow_down,
              // color: AppColors.primaryBlue,
            ),
          ),
          // Camera section
          if (showCameraIcon) ...[
            const Gap(16),
            PhotoUploadWidget(
              selectedImages: selectedImages,
              errorText: photoErrorText,
              onCameraPressed: () {
                if (onCameraTap != null) {
                  onCameraTap!();
                }
              },
              onImagesTap: () {
                if (onCameraTap != null) {
                  onCameraTap!();
                }
              },
            ),
          ],
        ],
      ),
    );
  }
}
