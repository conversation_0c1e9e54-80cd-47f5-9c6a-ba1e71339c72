// FQPDPage - Dynamic Question Part Management
//
// This page handles two types of question part management:
//
// 1. RestrictedMultiQuestion (isMulti=true AND multiMeasurementId!=0):
//    - Queries database for saved counter values from other questions
//    - Displays exact number of template dropdown widgets based on saved count
//    - User can select items to populate each template dropdown
//    - User CANNOT add or delete the item count (count is fixed)
//    - Uses normal questionPartId for questionpartMultiId
//
// 2. Non-restrictedMultiQuestion:
//    - Allows dynamic add/remove of question parts
//    - Each added item gets unique questionpartMultiId in "a-b" format
//    - User can add/remove items as needed
//
// Features:
// - Form data persistence with proper questionpartMultiId handling
// - Database integration using Realm
// - Navigation to QPMDPage with proper question part data
// - State management for dynamic UI updates

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';

@RoutePage()
class FQPDPage extends StatefulWidget {
  final Question? question;
  final num? taskId;
  final num? formId;

  const FQPDPage({
    super.key,
    this.question,
    this.taskId,
    this.formId,
  });

  @override
  State<FQPDPage> createState() => _FQPDPageState();
}

class _FQPDPageState extends State<FQPDPage> {
  // Available question parts from the passed Question
  List<QuestionPart> get _availableQuestionParts =>
      widget.question?.questionParts ?? [];

  // List of selected question parts for non-restrictedMultiQuestion
  final List<QuestionPart> _selectedQuestionParts = [];

  // List of template question parts for restrictedMultiQuestion
  final List<QuestionPart?> _templateQuestionParts = [];

  // Track if this is a restrictedMultiQuestion
  bool get _isRestrictedMultiQuestion {
    return widget.question?.isMulti == true &&
        widget.question?.multiMeasurementId != null &&
        widget.question?.multiMeasurementId != 0;
  }

  // Counter for generating unique questionpartMultiId
  int _nextMultiIdCounter = 1;

  @override
  void initState() {
    super.initState();
    _initializeQuestionParts();
    _loadSavedQuestionParts();
  }

  /// Initialize question parts based on question type
  void _initializeQuestionParts() {
    if (_isRestrictedMultiQuestion) {
      _initializeRestrictedMultiQuestion();
    }
    // For non-restrictedMultiQuestion, _selectedQuestionParts starts empty
  }

  /// Initialize restrictedMultiQuestion with saved value count
  void _initializeRestrictedMultiQuestion() {
    final savedValueCount = _getSavedValueCountForRestrictedMultiQuestion();
    debugPrint('RestrictedMultiQuestion saved value count: $savedValueCount');

    // Initialize template question parts list with the exact count
    // If no saved values, show at least one template dropdown
    final templateCount = savedValueCount > 0 ? savedValueCount : 1;
    _templateQuestionParts.clear();
    for (int i = 0; i < templateCount; i++) {
      _templateQuestionParts.add(null); // null means no selection yet
    }

    setState(() {});
  }

  /// Get saved value count for restrictedMultiQuestion from database
  int _getSavedValueCountForRestrictedMultiQuestion() {
    if (widget.taskId == null || widget.question?.questionId == null) {
      return 0;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return 0;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return 0;
      }

      // Look for QuestionAnswer entries for this restrictedMultiQuestion with counter values
      final questionAnswers = formModel.questionAnswers
          .where((answer) =>
              answer.questionId == widget.question!.questionId!.toInt())
          .toList();

      int maxCount = 0;
      for (final answer in questionAnswers) {
        if (answer.measurementTextResult != null) {
          final counterValue = int.tryParse(answer.measurementTextResult!);
          if (counterValue != null && counterValue > maxCount) {
            maxCount = counterValue;
          }
        }
      }

      debugPrint(
          'Found max counter value for restrictedMultiQuestion: $maxCount');
      return maxCount;
    } catch (e) {
      debugPrint(
          'Error getting saved value count for restrictedMultiQuestion: $e');
      return 0;
    }
  }

  /// Load saved question parts from database
  void _loadSavedQuestionParts() {
    if (widget.taskId == null ||
        widget.formId == null ||
        widget.question?.questionId == null) {
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return;
      }

      // Find saved question answers for this question
      final savedAnswers = formModel.questionAnswers
          .where((answer) =>
              answer.questionId == widget.question!.questionId!.toInt())
          .toList();

      if (savedAnswers.isEmpty) {
        debugPrint(
            'No saved answers found for question ${widget.question!.questionId}');
        return;
      }

      // Process saved answers based on question type
      if (_isRestrictedMultiQuestion) {
        _loadRestrictedMultiQuestionData(savedAnswers);
      } else {
        _loadNonRestrictedMultiQuestionData(savedAnswers);
      }

      setState(() {});
    } catch (e) {
      debugPrint('Error loading saved question parts: $e');
    }
  }

  /// Load data for restrictedMultiQuestion
  void _loadRestrictedMultiQuestionData(
      List<QuestionAnswerModel> savedAnswers) {
    // For restrictedMultiQuestion, load the selected question parts into template slots
    for (final answer in savedAnswers) {
      if (answer.questionpartId != null) {
        // Find the question part by ID
        final questionPart = _availableQuestionParts
            .where((qp) => qp.questionpartId == answer.questionpartId)
            .firstOrNull;

        if (questionPart != null) {
          // Find an empty template slot and assign this question part
          for (int i = 0; i < _templateQuestionParts.length; i++) {
            if (_templateQuestionParts[i] == null) {
              _templateQuestionParts[i] = questionPart;
              break;
            }
          }
        }
      }
    }
  }

  /// Load data for non-restrictedMultiQuestion
  void _loadNonRestrictedMultiQuestionData(
      List<QuestionAnswerModel> savedAnswers) {
    // For non-restrictedMultiQuestion, load selected question parts
    final uniqueQuestionPartIds = <num>{};

    for (final answer in savedAnswers) {
      if (answer.questionpartId != null &&
          !uniqueQuestionPartIds.contains(answer.questionpartId)) {
        uniqueQuestionPartIds.add(answer.questionpartId!);

        // Find the question part by ID
        final questionPart = _availableQuestionParts
            .where((qp) => qp.questionpartId == answer.questionpartId)
            .firstOrNull;

        if (questionPart != null &&
            !_selectedQuestionParts.any(
                (sp) => sp.questionpartId == questionPart.questionpartId)) {
          _selectedQuestionParts.add(questionPart);
        }
      }
    }

    // Update the counter for generating new multiIds
    if (_selectedQuestionParts.isNotEmpty) {
      _nextMultiIdCounter = _selectedQuestionParts.length + 1;
    }
  }

  /// Generate questionpartMultiId in "a-b" format
  String _generateQuestionPartMultiId(num? questionPartId) {
    if (_isRestrictedMultiQuestion) {
      // For restrictedMultiQuestion, use normal questionPartId
      return questionPartId?.toString() ?? '';
    } else {
      // For non-restrictedMultiQuestion, use "a-b" format
      final multiId = '${questionPartId ?? 0}-${_nextMultiIdCounter++}';
      return multiId;
    }
  }

  /// Remove selected item for non-restrictedMultiQuestion
  void _removeSelectedItem(int index) {
    setState(() {
      _selectedQuestionParts.removeAt(index);
    });
  }

  /// Show template item selection bottom sheet for restrictedMultiQuestion
  void _showTemplateItemBottomSheet(int templateIndex) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_availableQuestionParts.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select item',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _availableQuestionParts.length,
                  itemBuilder: (context, index) {
                    final questionPart = _availableQuestionParts[index];
                    final currentSelection =
                        _templateQuestionParts[templateIndex];
                    final isCurrentSelection =
                        currentSelection?.questionpartId ==
                            questionPart.questionpartId;

                    return InkWell(
                      onTap: () {
                        setState(() {
                          _templateQuestionParts[templateIndex] = questionPart;
                        });
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: AppColors.black,
                                    ),
                              ),
                            ),
                            if (isCurrentSelection)
                              const Icon(
                                Icons.radio_button_checked,
                                color: AppColors.primaryBlue,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.question?.questionDescription ?? 'Store details',
      ),
      body: _availableQuestionParts.isEmpty
          ? _buildEmptyState(textTheme)
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (_isRestrictedMultiQuestion) ...[
                    // RestrictedMultiQuestion: Show template dropdowns
                    ..._buildRestrictedMultiQuestionWidgets(textTheme),
                  ] else ...[
                    // Non-restrictedMultiQuestion: Show add dropdown and selected items
                    _buildMainDropdown(textTheme),
                    const Gap(16),

                    // Selected items list
                    if (_selectedQuestionParts.isNotEmpty) ...[
                      ..._selectedQuestionParts.asMap().entries.map((entry) {
                        final index = entry.key;
                        final questionPart = entry.value;
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: _buildSelectedItemCard(
                              questionPart, index, textTheme),
                        );
                      }),
                    ],
                  ],
                ],
              ),
            ),
    );
  }

  /// Build widgets for restrictedMultiQuestion
  List<Widget> _buildRestrictedMultiQuestionWidgets(TextTheme textTheme) {
    final widgets = <Widget>[];

    for (int i = 0; i < _templateQuestionParts.length; i++) {
      widgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: _buildTemplateDropdown(i, textTheme),
        ),
      );
    }

    return widgets;
  }

  /// Build a template dropdown for restrictedMultiQuestion
  Widget _buildTemplateDropdown(int index, TextTheme textTheme) {
    final selectedQuestionPart = _templateQuestionParts[index];

    return GestureDetector(
      onTap: selectedQuestionPart != null
          ? () {
              // Navigate to QPMDPage with the selected question part
              final questionPartForNavigation = QuestionPart(
                projectid: selectedQuestionPart.projectid,
                questionpartId: selectedQuestionPart.questionpartId,
                questionpartDescription:
                    selectedQuestionPart.questionpartDescription,
                price: selectedQuestionPart.price,
                modifiedTimeStampQuestionpart:
                    selectedQuestionPart.modifiedTimeStampQuestionpart,
                targetByCycle: selectedQuestionPart.targetByCycle,
                targetByGroup: selectedQuestionPart.targetByGroup,
                targetByCompany: selectedQuestionPart.targetByCompany,
                targetByRegion: selectedQuestionPart.targetByRegion,
                targetByBudget: selectedQuestionPart.targetByBudget,
                osaForm: selectedQuestionPart.osaForm,
                companyId: selectedQuestionPart.companyId,
                itemImage: selectedQuestionPart.itemImage,
                targeted: selectedQuestionPart.targeted,
                questionpartMultiId:
                    selectedQuestionPart.questionpartId?.toString(),
              );

              context.router.push(QPMDRoute(
                question: widget.question,
                questionPart: questionPartForNavigation,
                taskId: widget.taskId,
                formId: widget.formId,
              ));
            }
          : null,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: AppColors.black10,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            // Dropdown content
            Expanded(
              child: GestureDetector(
                onTap: () => _showTemplateItemBottomSheet(index),
                child: Text(
                  selectedQuestionPart?.questionpartDescription ??
                      'Please Select',
                  style: textTheme.montserratFormsField.copyWith(
                    color: selectedQuestionPart != null
                        ? AppColors.black
                        : AppColors.blackTint1,
                  ),
                ),
              ),
            ),

            // Dropdown arrow
            GestureDetector(
              onTap: () => _showTemplateItemBottomSheet(index),
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppColors.loginGreen,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(TextTheme textTheme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: AppColors.blackTint1,
            ),
            const Gap(16),
            Text(
              'No Question Parts Available',
              style: textTheme.montserratheadingmedium.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
            const Gap(8),
            Text(
              'This question does not have any question parts to display.',
              style: textTheme.montserratParagraphSmall.copyWith(
                color: AppColors.blackTint1,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainDropdown(TextTheme textTheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // Add button (+)
          GestureDetector(
            onTap: _showAddItemBottomSheet,
            child: Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                color: AppColors.loginGreen,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.add,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const Gap(12),

          // Dropdown content
          Expanded(
            child: Text(
              'Please Select',
              style: textTheme.montserratFormsField.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
          ),

          // Dropdown arrow
          GestureDetector(
            onTap: _showAddItemBottomSheet,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.loginGreen,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.keyboard_arrow_down,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedItemCard(
      QuestionPart questionPart, int index, TextTheme textTheme) {
    final hasImage = questionPart.itemImage != null &&
        questionPart.itemImage != '0' &&
        questionPart.itemImage!.isNotEmpty;

    return GestureDetector(
      onTap: () {
        // Create a copy with proper questionpartMultiId for navigation
        final questionPartForNavigation = QuestionPart(
          projectid: questionPart.projectid,
          questionpartId: questionPart.questionpartId,
          questionpartDescription: questionPart.questionpartDescription,
          price: questionPart.price,
          modifiedTimeStampQuestionpart:
              questionPart.modifiedTimeStampQuestionpart,
          targetByCycle: questionPart.targetByCycle,
          targetByGroup: questionPart.targetByGroup,
          targetByCompany: questionPart.targetByCompany,
          targetByRegion: questionPart.targetByRegion,
          targetByBudget: questionPart.targetByBudget,
          osaForm: questionPart.osaForm,
          companyId: questionPart.companyId,
          itemImage: questionPart.itemImage,
          targeted: questionPart.targeted,
          questionpartMultiId:
              _generateQuestionPartMultiId(questionPart.questionpartId),
        );

        // Navigate to QPMDPage with the current question
        context.router.push(QPMDRoute(
          question: widget.question,
          questionPart: questionPartForNavigation,
          taskId: widget.taskId,
          formId: widget.formId,
        ));
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: AppColors.black10,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            // Remove button (-)
            GestureDetector(
              onTap: () => _removeSelectedItem(index),
              behavior: HitTestBehavior.opaque,
              child: Container(
                width: 32,
                height: 32,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.remove,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
            const Gap(12),

            // Content area
            Expanded(
              child: Row(
                children: [
                  // Text content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Progress indicator (like in the image)
                        Text(
                          '${index + 1}/5',
                          style: textTheme.montserratParagraphXsmall.copyWith(
                            color: AppColors.blackTint1,
                          ),
                        ),
                        const Gap(4),
                        Text(
                          questionPart.questionpartDescription ??
                              'Unnamed Item',
                          style: textTheme.montserratFormsField,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // Image thumbnail (if available)
                  if (hasImage) ...[
                    const Gap(12),
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: AppColors.lightGrey1,
                        border: Border.all(color: AppColors.blackTint2),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          questionPart.itemImage!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: AppColors.lightGrey1,
                              child: const Icon(
                                Icons.image,
                                color: AppColors.blackTint1,
                                size: 24,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),

            const Gap(12),

            // Change selection dropdown arrow
            GestureDetector(
              onTap: () => _showChangeItemBottomSheet(index),
              behavior: HitTestBehavior.opaque,
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppColors.loginGreen,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddItemBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_availableQuestionParts.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select item',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _availableQuestionParts.length,
                  itemBuilder: (context, index) {
                    final questionPart = _availableQuestionParts[index];
                    final isAlreadySelected = _selectedQuestionParts.any(
                        (item) =>
                            item.questionpartId == questionPart.questionpartId);

                    return InkWell(
                      onTap: isAlreadySelected
                          ? null
                          : () {
                              setState(() {
                                _selectedQuestionParts.add(questionPart);
                              });
                              Navigator.pop(context);
                            },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: isAlreadySelected
                                          ? AppColors.blackTint1
                                          : AppColors.black,
                                    ),
                              ),
                            ),
                            if (isAlreadySelected)
                              const Icon(
                                Icons.check,
                                color: AppColors.loginGreen,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showChangeItemBottomSheet(int index) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_availableQuestionParts.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Change Selection',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _availableQuestionParts.length,
                  itemBuilder: (context, itemIndex) {
                    final questionPart = _availableQuestionParts[itemIndex];
                    final currentItem = _selectedQuestionParts[index];
                    final isCurrentSelection = currentItem.questionpartId ==
                        questionPart.questionpartId;
                    final isAlreadySelected = _selectedQuestionParts.any(
                        (item) =>
                            item.questionpartId == questionPart.questionpartId);

                    return InkWell(
                      onTap: isCurrentSelection
                          ? null
                          : () {
                              setState(() {
                                _selectedQuestionParts[index] = questionPart;
                              });
                              Navigator.pop(context);
                            },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: (isAlreadySelected &&
                                              !isCurrentSelection)
                                          ? AppColors.blackTint1
                                          : AppColors.black,
                                    ),
                              ),
                            ),
                            if (isCurrentSelection)
                              const Icon(
                                Icons.radio_button_checked,
                                color: AppColors.primaryBlue,
                                size: 20,
                              )
                            else if (isAlreadySelected)
                              const Icon(
                                Icons.check,
                                color: AppColors.loginGreen,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
