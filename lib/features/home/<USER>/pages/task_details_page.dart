// import 'dart:io';
// import 'package:auto_route/auto_route.dart';
// import 'package:dio/dio.dart';
// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:intl/intl.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:storetrack_app/config/routes/app_router.gr.dart';
// // No need to import form_page.dart directly
// import 'package:storetrack_app/config/themes/app_colors.dart';
// import 'package:storetrack_app/core/constants/app_assets.dart';
// import 'package:storetrack_app/core/extensions/theme_extensions.dart';
// import 'package:storetrack_app/core/utils/date_time_utils.dart';
// import 'package:storetrack_app/core/utils/snackbar_service.dart';
// import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
//     as entities;
// import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
// import 'package:storetrack_app/features/home/<USER>/widgets/segment_indicator.dart';
// import 'package:storetrack_app/features/home/<USER>/widgets/simple_notification_card.dart';
// import 'package:storetrack_app/features/notification/presentation/widgets/notification_card.dart';

// import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
// import 'package:webview_flutter/webview_flutter.dart';

// import '../widgets/date_converter.dart';

// @RoutePage()
// class TaskDetailsPage extends StatefulWidget {
//   final entities.TaskDetail task;

//   const TaskDetailsPage({
//     super.key,
//     required this.task,
//   });

//   @override
//   State<TaskDetailsPage> createState() => _TaskDetailsPageState();
// }

// class _TaskDetailsPageState extends State<TaskDetailsPage> {
//   int completedFormAmount = 0;
//   int maxFormAmount = 0;
//   bool showAlert = false;
//   bool showDocuments = false;

//   // Track expanded documents
//   Set<num?> expandedDocuments = {};

//   // Track expanded document names in Supporting section
//   Set<num?> expandedSupportingDocuments = {};

//   // Track the currently viewed document file
//   String? currentDocumentUrl;
//   bool showWebView = false;
//   WebViewController? webViewController;
//   bool isWebViewLoading = true;

//   // Track form display state
//   bool showFormQuestions = false;
//   entities.Document? currentFormDocument;

//   // Track brief overview expansion state
//   bool showBriefOverview = false;
//   int selectedBriefTab = 0; // 0 for Overview, 1 for Priorities
//   bool isPriorityExpanded = false; // Track if priority section is expanded

//   // Track brief section state
//   bool showBriefSection = false;
//   bool isOverviewExpanded = false;
//   bool isPrioritiesExpanded = false;

//   @override
//   void initState() {
//     super.initState();

//     // Calculate completed forms for progress indicator
//     if (widget.task.forms != null) {
//       for (var formModel in widget.task.forms!) {
//         if (formModel.isVisionForm == true) {
//           maxFormAmount++;

//           if (formModel.isMandatory == true ||
//               formModel.formCompleted == true) {
//             completedFormAmount++;
//           }
//         }
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: (showAlert) ? Colors.white : AppColors.lightGrey2,
//       appBar: CustomAppBar(
//         title: 'Task details',
//         actions: [
//           GestureDetector(
//             child: Image.asset(
//               AppAssets.alertIcon,
//               scale: 4,
//               color: showAlert ? AppColors.primaryBlue : AppColors.black,
//             ),
//             onTap: () {
//               setState(() {
//                 showAlert = !showAlert;
//                 showDocuments = false;
//                 // Track brief overview expansion state
//                 showBriefOverview = false;
//                 selectedBriefTab = 0; // 0 for Overview, 1 for Priorities
//                 isPriorityExpanded =
//                     false; // Track if priority section is expanded

//                 // Track brief section state
//                 showBriefSection = false;
//                 isOverviewExpanded = false;
//                 isPrioritiesExpanded = false;
//                 // If alerts are shown, hide documents
//                 if (showAlert) {
//                   showDocuments = false;
//                 }
//               });
//             },
//           ),
//           const Gap(18),
//           GestureDetector(
//             child: Image.asset(
//               AppAssets.documentsIcon,
//               color: showDocuments ? AppColors.primaryBlue : AppColors.black,
//               scale: 4,
//             ),
//             onTap: () {
//               setState(() {
//                 showDocuments = !showDocuments;
//                 showAlert = false;
//                 // Track brief overview expansion state
//                 showBriefOverview = false;
//                 selectedBriefTab = 0; // 0 for Overview, 1 for Priorities
//                 isPriorityExpanded =
//                     false; // Track if priority section is expanded

//                 // Track brief section state
//                 showBriefSection = false;
//                 isOverviewExpanded = false;
//                 isPrioritiesExpanded = false;
//                 // If documents are shown, hide alerts
//                 if (showDocuments) {
//                   showAlert = false;
//                 }
//               });
//             },
//           ),
//           const Gap(8),
//           PopupMenuButton<String>(
//             icon: Image.asset(
//               AppAssets.taskMore,
//               scale: 4,
//             ),
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(10),
//             ),
//             elevation: 8,
//             offset: const Offset(0, 20),
//             color: Colors.white,
//             position: PopupMenuPosition.under,
//             constraints: const BoxConstraints(
//               minWidth: 240,
//               maxWidth: 320,
//             ),
//             itemBuilder: (context) => [
//               _buildPopupMenuItem(context, 'Forms', AppAssets.taskForm),
//               const PopupMenuDivider(
//                 height: .2,
//               ),
//               _buildPopupMenuItem(context, 'POS', AppAssets.posIcon),
//               const PopupMenuDivider(
//                 height: .2,
//               ),
//               _buildPopupMenuItem(context, 'Notes', AppAssets.alertMessage),
//               const PopupMenuDivider(
//                 height: .2,
//               ),
//               _buildPopupMenuItem(context, 'Directions', AppAssets.appbarMap),
//               const PopupMenuDivider(
//                 height: .2,
//               ),
//               _buildPopupMenuItem(context, 'Store info', AppAssets.taskStore),
//               const PopupMenuDivider(
//                 height: .2,
//               ),
//               _buildPopupMenuItem(
//                   context, 'Store history', AppAssets.taskStoryHistory),
//               const PopupMenuDivider(
//                 height: .2,
//               ),
//               _buildPopupMenuItem(
//                   context, 'Task assistance', AppAssets.taskAssistant),
//               const PopupMenuDivider(
//                 height: .2,
//               ),
//               _buildPopupMenuItem(
//                   context, 'Complete task', AppAssets.taskComplete,
//                   isBlue: true),
//             ],
//             onSelected: (value) {
//               // Handle menu item selection
//               switch (value) {
//                 case 'Forms':
//                   // Navigate to Forms page with task data
//                   context.navigateTo(FormRoute(
//                     task: widget.task,
//                   ));
//                   break;
//                 case 'POS':
//                   // Navigate to POS page with task data
//                   context.navigateTo(PosRoute(
//                     task: widget.task,
//                   ));
//                   break;
//                 case 'Notes':
//                   // Navigate to Notes page with task data
//                   context.navigateTo(NotesRoute(
//                     task: widget.task,
//                   ));
//                   break;
//                 case 'Directions':
//                   _openGoogleMaps(widget.task);
//                   break;
//                 case 'Store info':
//                   context.navigateTo(const StoreInfoRoute(
//                       // task: widget.task,
//                       ));
//                   // Handle store info action
//                   break;
//                 case 'Store history':
//                   // Navigate to Store history page with task data
//                   context.navigateTo(StoreHistoryRoute(
//                     storeId: (widget.task.storeId ?? 0).toInt(),
//                     taskId: (widget.task.taskId ?? 0).toInt(),
//                   ));
//                   break;
//                 case 'Task assistance':
//                   // Handle task assistance action
//                   break;
//                 case 'Complete task':
//                   // Handle complete task action
//                   break;
//               }
//             },
//           ),
//         ],
//       ),
//       body: SingleChildScrollView(
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Store information card
//             _buildStoreInfoCard(context, widget.task),
//             showAlert
//                 ? Container(
//                     color: Colors.white,
//                     child: const Divider(),
//                   )
//                 : const Gap(16),

//             // Task alerts section
//             showAlert ? _buildTaskAlertsSection(context) : Container(),
//             showAlert ? const Gap(16) : Container(),

//             // Task documents section
//             (showDocuments) ? _buildTaskDocumentSection(context) : Container(),
//             showDocuments ? const Gap(16) : Container(),

//             // Task brief section
//             showBriefSection ? _buildTaskBriefSection(context) : Container(),
//             showBriefSection ? const Gap(16) : Container(),
//             // Progress cards row
//             (showAlert || showDocuments || showBriefSection)
//                 ? Container()
//                 : _buildProgressCards(
//                     context, widget.task, maxFormAmount, completedFormAmount),
//             (showAlert || showDocuments || showBriefSection)
//                 ? Container()
//                 : const Gap(16),

//             // Form queue and Forms tabs
//             (showAlert || showDocuments || showBriefSection)
//                 ? Container()
//                 : const Divider(color: AppColors.midGrey),
//             const Gap(8),
//             (showAlert || showDocuments || showBriefSection)
//                 ? Container()
//                 : _buildFormTabs(context, widget.task),
//             (showAlert || showDocuments || showBriefSection)
//                 ? Container()
//                 : const Gap(16),

//             // POS Received progress
//             (showAlert || showDocuments || showBriefSection)
//                 ? Container()
//                 : widget.task.forms?.length == 0
//                     ? const EmptyState(message: 'No forms available')
//                     : _buildPosReceivedProgress(context),
//             (showAlert || showDocuments || showBriefSection)
//                 ? Container()
//                 : const Gap(16),

//             // Overview and Documents tabs
//             (showAlert || showDocuments || showBriefSection)
//                 ? Container()
//                 : const Divider(color: AppColors.midGrey),

//             (showAlert || showDocuments || showBriefSection)
//                 ? Container()
//                 : _buildOverviewTabs(context),
//             (showAlert || showDocuments || showBriefSection)
//                 ? Container()
//                 : const Gap(16),
//           ],
//         ),
//       ),
//     );
//   }

//   // Open Google Maps with task coordinates
//   Future<void> _openGoogleMaps(entities.TaskDetail task) async {
//     try {
//       // Get coordinates from task
//       double? latitude;
//       double? longitude;

//       // Prefer taskLatitude/taskLongitude, fallback to latitude/longitude
//       if (task.taskLatitude != null && task.taskLongitude != null) {
//         latitude = task.taskLatitude!.toDouble();
//         longitude = task.taskLongitude!.toDouble();
//       } else if (task.latitude != null && task.longitude != null) {
//         latitude = task.latitude!.toDouble();
//         longitude = task.longitude!.toDouble();
//       }

//       if (latitude == null || longitude == null) {
//         if (mounted) {
//           SnackBarService.warning(
//             context: context,
//             message: 'Location coordinates not available for this task.',
//           );
//         }
//         return;
//       }

//       // Construct Google Maps URL
//       final googleMapsUrl =
//           'https://www.google.com/maps?q=$latitude,$longitude';

//       // Navigate to web browser with Google Maps URL
//       context.router.push(WebBrowserRoute(url: googleMapsUrl));
//     } catch (e) {
//       if (mounted) {
//         SnackBarService.error(
//           context: context,
//           message: 'Error opening map: ${e.toString()}',
//         );
//       }
//     }
//   }

//   Widget _buildStoreInfoCard(
//     BuildContext context,
//     entities.TaskDetail task,
//   ) {
//     final textTheme = Theme.of(context).textTheme;

//     return Container(
//       width: double.infinity,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(0),
//       ),
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Location icon and store name
//           Row(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               const Icon(
//                 Icons.location_on_outlined,
//                 size: 16,
//                 color: AppColors.black,
//               ),
//               const Gap(8),
//               Expanded(
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       task.location ?? "",
//                       style: textTheme.montserratTableSmall.copyWith(
//                         overflow: TextOverflow.ellipsis,
//                       ),
//                       maxLines: 2,
//                     ),
//                     const Gap(4),
//                     Text(
//                       task.storeName ?? "",
//                       style: textTheme.titleSmall?.copyWith(
//                         overflow: TextOverflow.ellipsis,
//                       ),
//                       maxLines: 1,
//                     ),
//                     const Gap(4),
//                     Text(
//                       task.storeGroup ?? "",
//                       style: textTheme.montserratTableSmall.copyWith(
//                         overflow: TextOverflow.ellipsis,
//                       ),
//                       maxLines: 2,
//                     ),
//                   ],
//                 ),
//               ),
//               // Client logo
//               task.clientLogoUrl?.isEmpty == true
//                   ? Container(
//                       width: 60,
//                       height: 60,
//                       decoration: BoxDecoration(
//                         // color: Colors.grey,
//                         borderRadius: BorderRadius.circular(32),
//                         border: Border.all(color: Colors.grey.shade300),
//                       ),
//                       child: const Center(child: Text('N/A')),
//                     )
//                   : Container(
//                       width: 60,
//                       height: 60,
//                       decoration: BoxDecoration(
//                         image: DecorationImage(
//                             image: NetworkImage(task.clientLogoUrl ?? '')),
//                         borderRadius: BorderRadius.circular(32),
//                         border: Border.all(color: Colors.grey.shade300),
//                       ),
//                     ),
//             ],
//           ),

//           const Gap(8),
//           const Divider(),
//           const Gap(8),

//           // Time and date
//           Row(
//             children: [
//               const Icon(
//                 Icons.access_time,
//                 size: 16,
//                 color: AppColors.black,
//               ),
//               const Gap(8),
//               Text(
//                 task.budget != null ? '${task.budget}m' : '',
//                 style: textTheme.montserratTableSmall
//                     .copyWith(fontWeight: FontWeight.w500, fontSize: 12),
//               ),
//               const Gap(16),
//               Image.asset(
//                 AppAssets.appbarCalendar,
//                 scale: 6,
//                 color: Colors.black,
//               ),
//               const Gap(8),
//               Text(
//                 task.scheduledTimeStamp != null
//                     ? convertToDateFormat(task.scheduledTimeStamp.toString())
//                     : '',
//                 style: textTheme.bodySmall,
//               ),
//               const Spacer(),
//               Text(
//                 'ID: ${task.taskId?.toString() ?? ''}',
//                 style: textTheme.bodySmall?.copyWith(
//                   color: AppColors.black,
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildProgressCards(
//     BuildContext context,
//     entities.TaskDetail task,
//     int max,
//     int completed,
//   ) {
//     final textTheme = Theme.of(context).textTheme;
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16.0),
//       child: Row(
//         children: [
//           // Task Completion card
//           Expanded(
//             child: Container(
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 22),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Text('Task Completion',
//                       style: textTheme.montserratTitleXxsmall),
//                   const Gap(8),
//                   Row(
//                     children: [
//                       Text(
//                         (completed == 0 && max == 0)
//                             ? '0%'
//                             : '${((completed / max) * 100).toStringAsFixed(0)}%',
//                         style: textTheme.titleSmall?.copyWith(
//                           fontWeight: FontWeight.w600,
//                         ),
//                       ),
//                     ],
//                   ),
//                   const Gap(8),
//                   // Progress bar with segmented indicator
//                   LayoutBuilder(
//                     builder: (context, constraints) {
//                       // Use the available width from constraints for the SegmentedProgressIndicator
//                       final progressWidth = constraints.maxWidth;

//                       // Center the line (line width is 1.5)
//                       final adjustedPosition = (completed == 0 && max == 0)
//                           ? 0.0
//                           : (completed / max) - 0.75;

//                       return Stack(
//                         clipBehavior: Clip.none,
//                         children: [
//                           // Base progress indicator
//                           SegmentedProgressIndicator(
//                             progress: (completed == 0 && max == 0)
//                                 ? 0.0
//                                 : (completed / max), // 40% progress
//                             totalWidth: progressWidth,
//                             activeColor: AppColors.primaryBlue,
//                             backgroundColor: Colors.grey.shade200,
//                             dividerColor: Colors.black,
//                             height: 10,
//                             segments: 10,
//                             borderRadius:
//                                 10, // Increased border radius for more curved edges
//                           ),

//                           // Vertical line
//                           Positioned(
//                             left:
//                                 adjustedPosition, // Using adjusted position to center the line
//                             top:
//                                 -8, // Increased top position to make the line longer
//                             bottom: 0, // Extend to the bottom of the stack
//                             child: Container(
//                               width: 1.5,
//                               color: Colors.black,
//                             ),
//                           ),
//                         ],
//                       );
//                     },
//                   ),
//                   const Gap(8),
//                   Text(
//                       '${task.ctFormsCompletedCnt ?? 0} of ${task.ctFormsTotalCnt ?? 0} forms',
//                       style: textTheme.montserratTableSmall),
//                 ],
//               ),
//             ),
//           ),

//           const Gap(16),

//           // POS Received card
//           Expanded(
//             child: Container(
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   Text('POS Received', style: textTheme.montserratTitleXxsmall),
//                   // const Gap(8),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       Image.asset(
//                         AppAssets.dashboardPos,
//                         width: 28,
//                       ),
//                       const Gap(8),
//                       Text(
//                         task.posReceived != null
//                             ? task.posReceived.toString()
//                             : '0',
//                         style: textTheme.titleSmall?.copyWith(
//                           fontSize: 40,
//                           fontWeight: FontWeight.w600,
//                         ),
//                       ),
//                       Center(
//                         child: SizedBox(
//                           height: 30,
//                           child: Column(
//                             mainAxisAlignment: MainAxisAlignment.end,
//                             children: [
//                               Text(
//                                 ' of ${task.posItems != null ? task.posItems!.length : '0'}',
//                                 style: textTheme.bodySmall?.copyWith(
//                                   color: AppColors.black,
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                   // const Gap(8),
//                   Text(
//                     'Delivered',
//                     style: textTheme.montserratTableSmall.copyWith(
//                         color: AppColors.primaryBlue,
//                         fontStyle: FontStyle.italic),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildFormTabs(BuildContext context, entities.TaskDetail task) {
//     final textTheme = Theme.of(context).textTheme;

//     return Column(
//       children: [
//         // Tabs
//         Padding(
//           padding: const EdgeInsets.only(left: 16.0, right: 8),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 'Form queue',
//                 style: textTheme.montserratTitleSmall,
//                 textAlign: TextAlign.center,
//               ),
//               const Spacer(),
//               const Icon(
//                 Icons.check_box_outlined,
//                 size: 16,
//               ),
//               const Gap(4),
//               Text('Forms', style: textTheme.montserratTitleExtraSmall),
//               const Gap(16),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildPosReceivedProgress(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     // Sample progress items - in a real app, these would come from your data model
//     final List progressItems = widget.task.forms ?? [];
//     print(
//         'items----$progressItems-----------------${widget.task.forms?.length}------------${widget.task.forms?.first.formName}');
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         // Horizontal scrollable list of progress items
//         SizedBox(
//           height: 100, // Fixed height for the scrollable container
//           child: ListView.builder(
//             scrollDirection: Axis.horizontal,
//             padding: const EdgeInsets.symmetric(horizontal: 8.0),
//             itemCount: progressItems.length,
//             itemBuilder: (context, index) {
//               // final item = progressItems[index];

//               return Container(
//                 width: MediaQuery.of(context).size.width *
//                     0.8, // Fixed width for each progress card
//                 margin:
//                     const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.circular(10),
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.black.withOpacity(0.05),
//                       blurRadius: 4,
//                       offset: const Offset(0, 2),
//                     ),
//                   ],
//                 ),
//                 padding: const EdgeInsets.all(16),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       progressItems[index].formName ?? '',
//                       style: textTheme.bodySmall?.copyWith(
//                         fontWeight: FontWeight.w600,
//                       ),
//                       maxLines: 1,
//                       overflow: TextOverflow.ellipsis,
//                     ),

//                     const Gap(12),

//                     // Progress bar
//                     Row(
//                       children: [
//                         Expanded(
//                           child: ClipRRect(
//                             borderRadius: BorderRadius.circular(4),
//                             child: LinearProgressIndicator(
//                               value: 0,
//                               backgroundColor: Colors.grey.shade200,
//                               valueColor: const AlwaysStoppedAnimation<Color>(
//                                   AppColors.primaryBlue),
//                               minHeight: 8,
//                             ),
//                           ),
//                         ),
//                         const Gap(24),
//                         Text(
//                           '0 of 0',
//                           style: textTheme.montserratTableSmall.copyWith(
//                             color: AppColors.black.withOpacity(0.6),
//                           ),
//                         ),
//                       ],
//                     ),

//                     const Gap(8),

//                     // Progress text
//                   ],
//                 ),
//               );
//             },
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildOverviewTabs(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return Column(
//       children: [
//         Padding(
//           padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Text(
//                 'Overview',
//                 style: textTheme.montserratTitleSmall,
//                 textAlign: TextAlign.center,
//               ),
//               const Spacer(),
//               Image.asset(
//                 AppAssets.documentsIcon,
//                 color: AppColors.black,
//                 scale: 5,
//               ),
//               const Gap(4),
//               Text('Documents',
//                   style: textTheme.montserratTitleExtraSmall.copyWith(
//                     color: AppColors.black,
//                   )),
//               const Gap(8),
//             ],
//           ),
//         ),
//         // Overview content
//         _buildOverviewContent(context),
//       ],
//     );
//   }

//   Widget _buildOverviewContent(BuildContext context) {
//     // Group alerts by date to get the latest notification
//     _groupTaskAlertsByDate();

//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16.0),
//       child: Container(
//         decoration: BoxDecoration(
//           color: Colors.white,
//           borderRadius: BorderRadius.circular(10),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               // Show the latest 1 notification if available
//               if (widget.task.taskalerts != null &&
//                   widget.task.taskalerts!.isNotEmpty) ...[
//                 // Get the latest notification (first one from the grouped alerts)
//                 Builder(builder: (context) {
//                   final latestEntry = groupedTaskAlerts.entries.first;
//                   final latestAlert = latestEntry.value.first;

//                   // Determine notification type based on content
//                   SimpleNotificationType notificationType =
//                       SimpleNotificationType.urgent;

//                   return SimpleNotificationCard(
//                     type: notificationType,
//                     message: latestAlert.message ?? 'No message',
//                   );
//                 }),
//               ],

//               if (widget.task.taskalerts != null)
//                 if (widget.task.taskalerts!.isEmpty) ...[
//                   Builder(builder: (context) {
//                     SimpleNotificationType notificationType =
//                         SimpleNotificationType.urgent;

//                     return SimpleNotificationCard(
//                       type: notificationType,
//                       message: 'No alerts available',
//                     );
//                   })
//                 ],

//               if (widget.task.taskalerts == null) ...[
//                 Builder(builder: (context) {
//                   SimpleNotificationType notificationType =
//                       SimpleNotificationType.urgent;

//                   return SimpleNotificationCard(
//                     type: notificationType,
//                     message: 'No alerts available',
//                   );
//                 })
//               ],
//               const Gap(16),

//               // Static notification cards
//               SimpleNotificationCard(
//                 type: SimpleNotificationType.manager,
//                 message: widget.task.taskNote!.isNotEmpty
//                     ? widget.task.taskNote!
//                     : 'No notes available',
//               ),

//               const Gap(16),

//               // Show a question and answer from forms if available
//               Builder(
//                 builder: (context) {
//                   // Fallback to form name and brief URL if no questions available
//                   if (widget.task.forms != null &&
//                       widget.task.forms!.isNotEmpty) {
//                     return const SimpleNotificationCard(
//                       type: SimpleNotificationType.info,
//                       title: 'Brief summary',
//                       message: 'The summary of the brief will be shown here',
//                     );
//                   }

//                   // Final fallback if no forms available
//                   return const SimpleNotificationCard(
//                     type: SimpleNotificationType.info,
//                     title: 'Brief',
//                     message: 'No brief available',
//                   );
//                 },
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   PopupMenuItem<String> _buildPopupMenuItem(
//       BuildContext context, String title, String icon,
//       {bool isBlue = false}) {
//     return PopupMenuItem<String>(
//       value: title,
//       height: 48, // Set a consistent height for all menu items
//       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Text(
//             title,
//             style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
//                   color: isBlue ? AppColors.primaryBlue : Colors.black,
//                 ),
//           ),
//           const SizedBox(width: 16),
//           Container(
//             width: 20,
//             height: 20,
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(4),
//               // border: Border.all(color: Colors.grey.shade400, width: 1.5),
//             ),
//             child: Center(
//               child: Image.asset(
//                 icon,
//                 scale: 3,
//                 color: Colors.black,
//                 fit: BoxFit.cover,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   // Map to store alerts grouped by date
//   Map<String, List<entities.Taskalert>> groupedTaskAlerts = {};

//   // Set to track which forms are expanded
//   Set<int> expandedForms = {};

//   // Set to track which document sections are expanded
//   Set<String> expandedDocumentSections = {};

//   // Function to group alerts by date
//   void _groupTaskAlertsByDate() {
//     groupedTaskAlerts.clear();

//     // For demonstration purposes, we'll create a fake date for each alert
//     // In a real app, you would use the actual date from the alert
//     for (var alert in widget.task.taskalerts ?? []) {
//       // Create a fake date for demonstration (in a real app, use alert.date)
//       // Spread alerts across the last 7 days for demonstration
//       final index = widget.task.taskalerts!.indexOf(alert);
//       final daysAgo = index % 3; // Spread across 3 days
//       final fakeDate = DateTime.now().subtract(Duration(days: daysAgo));

//       // Format the date string for display and grouping
//       final formattedDate = DateFormat('EEE dd MMM').format(fakeDate);

//       if (!groupedTaskAlerts.containsKey(formattedDate)) {
//         groupedTaskAlerts[formattedDate] = [];
//       }

//       groupedTaskAlerts[formattedDate]!.add(alert);
//     }
//   }

//   Widget _buildTaskAlertsSection(BuildContext context) {
//     print('alert-------${widget.task.taskalerts}');
//     final textTheme = Theme.of(context).textTheme;

//     // Group alerts by date
//     _groupTaskAlertsByDate();

//     return Container(
//       width: double.infinity,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(10),
//       ),
//       padding: const EdgeInsets.all(0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // List of task alerts or empty state
//           // Always show notifications, even if empty
//           (widget.task.taskalerts == null || widget.task.taskalerts!.isEmpty)
//               ? const EmptyState(message: 'No task alerts available')
//               : Padding(
//                   padding: const EdgeInsets.all(0),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       // List of task alerts grouped by date
//                       ...groupedTaskAlerts.entries.map((entry) {
//                         final date = entry.key;
//                         final alertsForThisDate = entry.value;
//                         final isToday = date ==
//                             DateFormat('EEE dd MMM').format(DateTime.now());

//                         return Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             // Date header
//                             Padding(
//                               padding: const EdgeInsets.fromLTRB(
//                                 16,
//                                 16,
//                                 16,
//                                 12,
//                               ),
//                               child: Text(
//                                 date,
//                                 style: textTheme.titleSmall?.copyWith(
//                                   color: isToday
//                                       ? AppColors.primaryBlue
//                                       : AppColors.black,
//                                 ),
//                               ),
//                             ),

//                             // Alerts for this date
//                             ...alertsForThisDate.map((alert) {
//                               // Determine notification type based on content
//                               NotificationType notificationType =
//                                   NotificationType.message;

//                               if (alert.subject
//                                           ?.toLowerCase()
//                                           .contains('urgent') ==
//                                       true ||
//                                   alert.message
//                                           ?.toLowerCase()
//                                           .contains('urgent') ==
//                                       true ||
//                                   alert.message
//                                           ?.toLowerCase()
//                                           .contains('asap') ==
//                                       true) {
//                                 notificationType = NotificationType.urgent;
//                               }

//                               // Calculate a fake time ago for demonstration
//                               final index = alertsForThisDate.indexOf(alert);
//                               final hoursAgo = 1 +
//                                   (index * 2); // Spread across different hours
//                               final fakeDate = DateTime.now()
//                                   .subtract(Duration(hours: hoursAgo));
//                               final timeAgo = getTimeAgo(fakeDate);

//                               return NotificationCard(
//                                 type: notificationType,
//                                 message: alert.message ?? 'No message',
//                                 company: widget.task.client ?? 'Unknown',
//                                 task: alert.subject ?? 'No subject',
//                                 location: widget.task.location ?? 'No location',
//                                 timeAgo: timeAgo,
//                                 duration: widget.task.budget != null
//                                     ? '${widget.task.budget}m'
//                                     : '0m',
//                               );
//                             }),

//                             // Add divider between date groups
//                             if (entry.key != groupedTaskAlerts.keys.last)
//                               Padding(
//                                 padding: const EdgeInsets.only(
//                                     top: 8.0, bottom: 16.0),
//                                 child: Divider(
//                                   height: 1,
//                                   thickness: 1,
//                                   color: AppColors.appBarBorderBlack,
//                                 ),
//                               ),
//                           ],
//                         );
//                       }),
//                     ],
//                   ),
//                 ),
//         ],
//       ),
//     );
//   }

//   Widget _buildTaskDocumentSection(BuildContext context) {
//     // If showing webview, display the webview instead of document list
//     if (showWebView && currentDocumentUrl != null) {
//       return _buildDocumentWebView(context);
//     }

//     // If showing form questions, display the form questions and answers
//     if (showFormQuestions) {
//       return _buildFormQuestionsView(context);
//     }

//     return Container(
//       width: double.infinity,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(0),
//       ),
//       // padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // List of documents or empty state
//           widget.task.documents == null
//               ? const Padding(
//                   padding: EdgeInsets.all(0),
//                   child: EmptyState(message: 'No documents available'),
//                 )
//               : Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     // Document categories
//                     Padding(
//                       padding: const EdgeInsets.only(
//                           top: 20, left: 16, right: 16, bottom: 8),
//                       child: _buildDocumentCategory(true, context, 'Main',
//                           [] // Always empty for main section as we'll show Brief statically
//                           ),
//                     ),

//                     // Show divider only if at least one section is expanded
//                     // if (expandedDocumentSections.contains('Main') ||
//                     //     expandedDocumentSections.contains('Supporting'))
//                     Divider(
//                       color: AppColors.black10,
//                       height: 1,
//                       thickness: 1,
//                     ),

//                     Padding(
//                       padding: const EdgeInsets.only(
//                           bottom: 12, left: 16, right: 16, top: 20),
//                       child: _buildDocumentCategory(
//                         false,
//                         context,
//                         'Supporting',
//                         widget.task.documents!
//                             // .where((doc) => doc.documentTypeId == 2)
//                             .toList(),
//                       ),
//                     ),
//                   ],
//                 ),
//         ],
//       ),
//     );
//   }

//   // New method to build the document webview
//   Widget _buildDocumentWebView(BuildContext context) {
//     return Container(
//       width: double.infinity,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(10),
//       ),
//       padding: const EdgeInsets.all(8),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Header with back button and refresh button
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               IconButton(
//                 icon: const Icon(Icons.arrow_back),
//                 onPressed: () {
//                   setState(() {
//                     showWebView = false;
//                     currentDocumentUrl = null;
//                     webViewController = null;
//                   });
//                 },
//               ),
//               Text(
//                 'Document Viewer',
//                 style: Theme.of(context).textTheme.titleMedium?.copyWith(
//                       fontWeight: FontWeight.w600,
//                     ),
//               ),
//               IconButton(
//                 icon: const Icon(Icons.refresh),
//                 onPressed: () {
//                   webViewController?.reload();
//                 },
//               ),
//             ],
//           ),

//           // WebView container with fixed height
//           Container(
//             height: MediaQuery.of(context).size.height *
//                 0.6, // 60% of screen height
//             decoration: BoxDecoration(
//               border: Border.all(color: Colors.grey.shade300),
//               borderRadius: BorderRadius.circular(10),
//             ),
//             child: ClipRRect(
//               borderRadius: BorderRadius.circular(10),
//               child: Stack(
//                 children: [
//                   WebViewWidget(
//                     controller: webViewController ?? _initWebViewController(),
//                   ),
//                   if (isWebViewLoading)
//                     const Center(
//                       child: CircularProgressIndicator(
//                         color: AppColors.primaryBlue,
//                       ),
//                     ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   // New method to build the form questions view
//   Widget _buildFormQuestionsView(BuildContext context) {
//     return Container(
//       width: double.infinity,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(10),
//       ),
//       padding: const EdgeInsets.all(8),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Header with back button
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               IconButton(
//                 icon: const Icon(Icons.arrow_back),
//                 onPressed: () {
//                   setState(() {
//                     showFormQuestions = false;
//                     currentFormDocument = null;
//                     // Keep showDocuments = true to return to document list
//                   });
//                 },
//               ),
//               // Text(
//               //   'Form Questions & Answers',
//               //   style: Theme.of(context).textTheme.titleMedium?.copyWith(
//               //         fontWeight: FontWeight.w600,
//               //       ),
//               // ),
//               const SizedBox(width: 48), // Placeholder for symmetry
//             ],
//           ),

//           // Form questions and answers content
//           Container(
//             constraints: BoxConstraints(
//               maxHeight: MediaQuery.of(context).size.height * 0.6,
//             ),
//             child: SingleChildScrollView(
//               child: _buildFormQuestionsContent(context),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   // Helper method to build form questions content
//   Widget _buildFormQuestionsContent(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     // Get all forms from the task
//     if (widget.task.forms == null || widget.task.forms!.isEmpty) {
//       return Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Text(
//           'No form questions available for this task.',
//           style: textTheme.bodyMedium?.copyWith(
//             color: AppColors.black.withOpacity(0.6),
//           ),
//         ),
//       );
//     }

//     List<Widget> formWidgets = [];

//     for (var form in widget.task.forms!) {
//       if (form.questions != null && form.questions!.isNotEmpty) {
//         formWidgets.add(_buildFormSection(context, form));
//         formWidgets.add(const SizedBox(height: 16));
//       }
//     }

//     if (formWidgets.isEmpty) {
//       return Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Text(
//           'No questions found in the forms.',
//           style: textTheme.bodyMedium?.copyWith(
//             color: AppColors.black.withOpacity(0.6),
//           ),
//         ),
//       );
//     }

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: formWidgets,
//     );
//   }

//   // Helper method to build a single form section
//   Widget _buildFormSection(BuildContext context, entities.Form form) {
//     final textTheme = Theme.of(context).textTheme;
//     final formId = (form.formId ?? form.hashCode)
//         .toInt(); // Use formId or hashCode as unique identifier
//     final isExpanded = expandedForms.contains(formId);

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         // Form header - now tappable
//         GestureDetector(
//           onTap: () {
//             setState(() {
//               if (isExpanded) {
//                 expandedForms.remove(formId);
//               } else {
//                 expandedForms.add(formId);
//               }
//             });
//           },
//           child: Container(
//             width: double.infinity,
//             padding: const EdgeInsets.all(16.0),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: isExpanded
//                   ? const BorderRadius.only(
//                       topLeft: Radius.circular(10),
//                       topRight: Radius.circular(10),
//                     )
//                   : BorderRadius.circular(8),
//             ),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Expanded(
//                   child: Text(
//                     form.formName ?? 'Unnamed Form',
//                     style: textTheme.titleSmall?.copyWith(
//                       fontWeight: FontWeight.w600,
//                     ),
//                   ),
//                 ),
//                 Icon(
//                   isExpanded
//                       ? Icons.keyboard_arrow_up
//                       : Icons.keyboard_arrow_right,
//                   color: AppColors.black,
//                   size: 20,
//                 ),
//               ],
//             ),
//           ),
//         ),

//         // Questions and answers - only show when expanded
//         if (isExpanded) ...[
//           ...form.questions!.asMap().entries.map((entry) {
//             final index = entry.key;
//             final question = entry.value;
//             final isLastQuestion = index == form.questions!.length - 1;
//             return _buildQuestionAnswerItem(
//                 context, form, question, isLastQuestion);
//           }),
//         ],
//       ],
//     );
//   }

//   // Helper method to build a question and answer item
//   Widget _buildQuestionAnswerItem(
//       BuildContext context, entities.Form form, entities.Question question,
//       [bool isLastQuestion = false]) {
//     final textTheme = Theme.of(context).textTheme;

//     // Find the corresponding answer for this question
//     entities.QuestionAnswer? answer;
//     if (form.questionAnswers != null) {
//       try {
//         answer = form.questionAnswers!.firstWhere(
//           (qa) => qa.questionId == question.questionId,
//         );
//       } catch (e) {
//         // No answer found for this question
//         answer = null;
//       }
//     }

//     return Container(
//       padding: const EdgeInsets.all(16.0),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: isLastQuestion
//             ? const BorderRadius.only(
//                 bottomLeft: Radius.circular(10),
//                 bottomRight: Radius.circular(10),
//               )
//             : null,
//         // border: Border(
//         //   bottom: BorderSide(color: Colors.grey.shade200),
//         //   left: BorderSide(color: Colors.grey.shade300),
//         //   right: BorderSide(color: Colors.grey.shade300),
//         // ),
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Question
//           Text(
//             question.questionBrief ?? 'No question description',
//             style: textTheme.bodyMedium?.copyWith(
//               fontWeight: FontWeight.w500,
//               color: AppColors.black,
//             ),
//           ),
//           const SizedBox(height: 8),

//           // Answer
//           Text(
//             _getAnswerText(answer),
//             style: textTheme.bodyMedium?.copyWith(
//               color: AppColors.black.withOpacity(0.8),
//             ),
//           ),

//           // Show question brief if available
//           if (question.questionBrief?.isNotEmpty == true) ...[
//             const SizedBox(height: 8),
//             Container(
//               padding: const EdgeInsets.all(8.0),
//               decoration: BoxDecoration(
//                 color: AppColors.lightGrey2.withOpacity(0.3),
//                 borderRadius: BorderRadius.circular(4),
//               ),
//               child: Text(
//                 'Brief: ${question.questionBrief}',
//                 style: textTheme.bodySmall?.copyWith(
//                   color: AppColors.black.withOpacity(0.7),
//                   fontStyle: FontStyle.italic,
//                 ),
//               ),
//             ),
//           ],
//         ],
//       ),
//     );
//   }

//   // Helper method to get answer text from QuestionAnswer
//   String _getAnswerText(entities.QuestionAnswer? answer) {
//     if (answer == null) {
//       return 'No answer ';
//     }

//     if (answer.measurementTextResult?.isNotEmpty == true) {
//       return answer.measurementTextResult!;
//     }

//     if (answer.measurementOptionId != null) {
//       return 'Option ${answer.measurementOptionId}';
//     }

//     if (answer.measurementOptionIds?.isNotEmpty == true) {
//       return 'Options: ${answer.measurementOptionIds}';
//     }

//     return 'Answer provided (no text available)';
//   }

//   // Helper method to initialize the WebViewController
//   WebViewController _initWebViewController() {
//     webViewController = WebViewController()
//       ..setJavaScriptMode(JavaScriptMode.unrestricted)
//       ..setNavigationDelegate(
//         NavigationDelegate(
//           onPageStarted: (String url) {
//             setState(() {
//               isWebViewLoading = true;
//             });
//           },
//           onPageFinished: (String url) {
//             setState(() {
//               isWebViewLoading = false;
//             });
//           },
//           onWebResourceError: (WebResourceError error) {
//             setState(() {
//               isWebViewLoading = false;
//               // webViewHasError = true;
//             });
//           },
//         ),
//       )
//       ..loadRequest(Uri.parse(currentDocumentUrl!));

//     return webViewController!;
//   }

//   Widget _buildDocumentCategory(
//     bool isMainSection,
//     BuildContext context,
//     String title,
//     List<entities.Document> documents,
//   ) {
//     final textTheme = Theme.of(context).textTheme;
//     final isExpanded = expandedDocumentSections.contains(title);

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         // Category title - now tappable
//         GestureDetector(
//           onTap: () {
//             setState(() {
//               if (isExpanded) {
//                 expandedDocumentSections.remove(title);
//               } else {
//                 expandedDocumentSections.add(title);
//               }
//             });
//           },
//           child: Container(
//             color: Colors.transparent,
//             width: double.infinity,
//             padding: const EdgeInsets.symmetric(vertical: 0.0),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(title, style: textTheme.montserratTitleSmall),
//                 Icon(
//                   isExpanded
//                       ? Icons.keyboard_arrow_up
//                       : Icons.keyboard_arrow_right,
//                   color: AppColors.black,
//                   size: 20,
//                 ),
//               ],
//             ),
//           ),
//         ),
//         const SizedBox(height: 12),

//         // Content - only show when expanded
//         if (isExpanded) ...[
//           // For main section, always show Brief item
//           if (isMainSection)
//             _buildBriefItem(context)
//           else
//             // For supporting documents, show document names with expandable files
//             documents.isEmpty
//                 ? EmptyState(
//                     message: 'No ${title.toLowerCase()} documents available',
//                     margin: const EdgeInsets.symmetric(vertical: 8),
//                   )
//                 : Column(
//                     children: documents.map((document) {
//                       return _buildDocumentNameItem(context, document);
//                     }).toList(),
//                   ),
//         ],
//       ],
//     );
//   }

//   // Build the static Brief item for the main section
//   Widget _buildBriefItem(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return Container(
//       margin: const EdgeInsets.only(bottom: 12.0),
//       decoration: BoxDecoration(
//         border: Border.all(color: Colors.grey.shade300),
//         borderRadius: BorderRadius.circular(10),
//       ),
//       child: ListTile(
//         leading: Padding(
//           padding: const EdgeInsets.only(left: 14.0),
//           child: Image.asset(
//             AppAssets.supportDocument,
//             scale: 4,
//             color: Colors.black,
//           ),
//         ),
//         title: Text(
//           'Brief',
//           style: textTheme.montserratTitleExtraSmall.copyWith(
//             color: Colors.black,
//           ),
//         ),
//         onTap: () {
//           setState(() {
//             showBriefSection = true;
//             showFormQuestions = false;
//             showWebView = false;
//             showDocuments = false; // Hide documents section
//             showAlert = false; // Hide alerts when showing brief
//           });
//         },
//       ),
//     );
//   }

//   // Build task brief section with expandable Overview and Priority items
//   Widget _buildTaskBriefSection(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 0.0),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(0),
//       ),
//       child: Column(
//         children: [
//           // Overview item
//           Container(
//             decoration: const BoxDecoration(
//               border: Border(
//                   // bottom: BorderSide(color: Colors.grey.shade200),
//                   ),
//             ),
//             child: ListTile(
//               // leading: const Icon(
//               //   Icons.info_outline,
//               //   color: AppColors.primaryBlue,
//               //   size: 20,
//               // ),
//               title: Text(
//                 'Overview',
//                 style: textTheme.titleSmall?.copyWith(
//                   fontWeight: FontWeight.w600,
//                   color: AppColors.black,
//                 ),
//               ),
//               trailing: Icon(
//                 isOverviewExpanded
//                     ? Icons.keyboard_arrow_up
//                     : Icons.keyboard_arrow_right,
//                 color: AppColors.black,
//                 size: 20,
//               ),
//               onTap: () {
//                 setState(() {
//                   isOverviewExpanded = !isOverviewExpanded;
//                 });
//               },
//             ),
//           ),

//           // Overview content
//           if (isOverviewExpanded)
//             Container(
//               padding: const EdgeInsets.all(16.0),
//               decoration: BoxDecoration(
//                 border: Border(
//                   bottom: BorderSide(color: Colors.grey.shade200),
//                 ),
//               ),
//               child: _buildOverviewTabContent(context),
//             ),
// // Divider(),
//           // Priority item
//           ListTile(
//             // leading: const Icon(
//             //   Icons.priority_high,
//             //   color: AppColors.primaryBlue,
//             //   size: 20,
//             // ),
//             title: Text(
//               'Brief',
//               style: textTheme.titleSmall?.copyWith(
//                 fontWeight: FontWeight.w600,
//                 color: AppColors.black,
//               ),
//             ),
//             trailing: Icon(
//               isPrioritiesExpanded
//                   ? Icons.keyboard_arrow_up
//                   : Icons.keyboard_arrow_right,
//               color: AppColors.black,
//               size: 20,
//             ),
//             onTap: () {
//               setState(() {
//                 isPrioritiesExpanded = !isPrioritiesExpanded;
//               });
//             },
//           ),

//           // Priority content
//           if (isPrioritiesExpanded)
//             Container(
//               padding: const EdgeInsets.all(16.0),
//               child: _buildPrioritiesTabContent(context),
//             ),
//         ],
//       ),
//     );
//   }

//   // Build tab button
//   Widget _buildTabButton(String title, int tabIndex) {
//     final textTheme = Theme.of(context).textTheme;
//     final isSelected = selectedBriefTab == tabIndex;

//     return GestureDetector(
//       onTap: () {
//         setState(() {
//           selectedBriefTab = tabIndex;
//         });
//       },
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
//         decoration: BoxDecoration(
//           color: isSelected ? AppColors.primaryBlue : Colors.transparent,
//           borderRadius: BorderRadius.circular(10),
//           border: Border.all(
//             color: isSelected ? AppColors.primaryBlue : Colors.grey.shade300,
//           ),
//         ),
//         child: Text(
//           title,
//           style: textTheme.bodySmall?.copyWith(
//             color: isSelected ? Colors.white : AppColors.black,
//             fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
//           ),
//         ),
//       ),
//     );
//   }

//   // Build overview tab content
//   Widget _buildOverviewTabContent(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return SizedBox(
//       width: double.infinity,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 'Exciting Task Alert!',
//                 style: textTheme.titleSmall?.copyWith(
//                   fontWeight: FontWeight.w600,
//                   color: AppColors.black,
//                 ),
//               ),
//               const Gap(12),
//               Text(
//                 "The summary of the task brief goes here. \n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl nec ultricies tincidunt, nunc nisl tincidunt nunc, nec tincidunt nisl nisl nec nisl. \n\nNullam euismod, nisl nec ultricies tincidunt, nunc nisl tincidunt nunc, nec tincidunt nisl nisl nec nisl.",
//                 style: textTheme.bodySmall?.copyWith(
//                   color: AppColors.black.withValues(alpha: 0.8),
//                   height: 1.4,
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }

//   // Build priorities tab content
//   Widget _buildPrioritiesTabContent(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         // Priority Forms List - always show all questions and answers
//         if (widget.task.forms != null && widget.task.forms!.isNotEmpty)
//           ...widget.task.forms!.asMap().entries.map((entry) {
//             final index = entry.key;
//             final form = entry.value;

//             return _buildPriorityFormItem(context, index + 1, form);
//           })
//         else
//           Container(
//             padding: const EdgeInsets.all(16.0),
//             decoration: BoxDecoration(
//               color: Colors.grey.shade50,
//               borderRadius: BorderRadius.circular(10),
//               border: Border.all(color: Colors.grey.shade200),
//             ),
//             child: Text(
//               'No priority forms available',
//               style: textTheme.bodySmall?.copyWith(
//                 color: AppColors.black.withValues(alpha: 0.6),
//               ),
//             ),
//           ),
//       ],
//     );
//   }

//   // Build priority form item - always show all questions
// //   Widget _buildPriorityFormItem(
// //       BuildContext context, int priorityNumber, entities.Form form) {
// //     final textTheme = Theme.of(context).textTheme;
// //     final isLastItem = priorityNumber == (widget.task.forms?.length ?? 0);

// //     return Column(
// //       children: [

// //         // Priority header with connecting line
// //         Row(
// //           children: [
// //             // Column containing the circle and connecting line
// //             SizedBox(
// //               width: 24,
// //               child: Column(
// //                 children: [
// //                   // Priority number circle
// //                   Row(
// //                     children: [
// //                       Container(
// //                         width: 24,
// //                         height: 24,
// //                         decoration: BoxDecoration(
// //                           border: Border.all(color: AppColors.black20),
// //                           // color: AppColors.primaryBlue,
// //                           shape: BoxShape.circle,
// //                         ),
// //                         child: Center(
// //                           child: Text(
// //                             priorityNumber.toString(),
// //                             style: textTheme.bodySmall?.copyWith(
// //                               color: AppColors.primaryBlue,
// //                               fontWeight: FontWeight.w600,
// //                               fontSize: 12,
// //                             ),
// //                           ),
// //                         ),
// //                       ),
// //                     ],
// //                   ),
// //                   // Connecting line below the circle (only if not the last item)
// //                   if (!isLastItem)
// //                     Container(
// //                       width: 2,
// //                       height: 60,
// //                       color: AppColors.black20,
// //                       margin: const EdgeInsets.only(top: 0),
// //                     ),
// //                 ],
// //               ),
// //             ),
// //             const Gap(12),

// //             // Form name and description
// //             Expanded(
// //               child: Column(
// //                 crossAxisAlignment: CrossAxisAlignment.start,
// //                 children: [
// //                   Text(
// //                     form.formName ?? 'Unnamed Form',
// //                     style: textTheme.bodyMedium?.copyWith(
// //                       fontWeight: FontWeight.w600,
// //                       color: AppColors.black,
// //                     ),
// //                   ),

// //                 ],
// //               ),
// //             ),
// //           ],
// //         ),

// //         // Questions list - always show all questions
// //         const Gap(8),
// //         if (form.questions == null || form.questions!.isEmpty)
// //           Container(
// //             padding: const EdgeInsets.all(16.0),
// //             decoration: BoxDecoration(
// //               color: Colors.grey.shade50,
// //               borderRadius: BorderRadius.circular(6),
// //             ),
// //             child: Text(
// //               'No questions available for this form',
// //               style: textTheme.bodySmall?.copyWith(
// //                 color: AppColors.black.withValues(alpha: 0.6),
// //               ),
// //             ),
// //           )
// //         else
// //           // Show all questions
// //           ...form.questions!.asMap().entries.map((entry) {

// //           if(entry.value.questionBrief?.isNotEmpty == true){

// //             final index = entry.key;
// //             final question = entry.value;
// //             final isLastQuestion = index == form.questions!.length - 1;
// //             return _buildPriorityQuestionItem(
// //                 context, form, question, isLastQuestion);
// //           }
// //           else{
// //             return Container();
// //           }
// //           }),

// //           // ),
// //             if( form.questions!.every(
// //   (q) => q.questionBrief?.isEmpty ?? true ,
// // ))
// // if(!(form.questions == null || form.questions!.isEmpty))
// //  Align(alignment: Alignment.centerLeft,
// //   child: Padding(
// //     padding: const EdgeInsets.symmetric(horizontal: 20.0,vertical: 12.0),
// //     child:Container(
// //             padding: const EdgeInsets.all(16.0),
// //             decoration: BoxDecoration(
// //               color: Colors.grey.shade50,
// //               borderRadius: BorderRadius.circular(6),
// //             ),
// //             child: Text(
// //               'No brief available for this form',
// //               style: textTheme.bodySmall?.copyWith(
// //                 color: AppColors.black.withValues(alpha: 0.6),
// //               ),
// //             ),
// //           )
// //   ))

// //       ],
// //     );
// //   }
//   Widget _buildPriorityFormItem(
//     BuildContext context,
//     int priorityNumber,
//     entities.Form form,
//   ) {
//     final textTheme = Theme.of(context).textTheme;
//     final visibleQuestions = form.questions
//         ?.where((q) => q.questionBrief?.isNotEmpty == true)
//         .toList();
//     return Row(
//       children: [
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Priority header with connecting line
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 // Priority number circle
//                 Container(
//                   width: 24,
//                   height: 24,
//                   decoration: BoxDecoration(
//                     border: Border.all(color: AppColors.black20),
//                     shape: BoxShape.circle,
//                   ),
//                   child: Center(
//                     child: Text(
//                       priorityNumber.toString(),
//                       style: textTheme.bodySmall?.copyWith(
//                         color: AppColors.primaryBlue,
//                         fontWeight: FontWeight.w600,
//                         fontSize: 12,
//                       ),
//                     ),
//                   ),
//                 ),
//                 const Gap(12),
//                 // Form name
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       form.formName ?? 'Unnamed Form',
//                       style: textTheme.bodyMedium?.copyWith(
//                         fontWeight: FontWeight.w600,
//                         color: AppColors.black,
//                       ),
//                     ),
//                     // Brief question
//                   ],
//                 ),
//               ],
//             ),
//             // Connecting line below the circle (only if not the last item)
//             Row(
//               children: [
//                 if (!(form.questions == null || form.questions!.isEmpty))
//                   Padding(
//                     padding: const EdgeInsets.only(left: 10.0),
//                     child: Container(
//                       width: 2,
//                       height: 60,
//                       color: AppColors.black10,
//                       margin: const EdgeInsets.only(top: 0),
//                     ),
//                   ),
//                 priorityNumber == (widget.task.forms?.length ?? 0)
//                     ? const Gap(36)
//                     : const Gap(26),
//                 ...form.questions!.asMap().entries.map(
//                   (entry) {
//                     if (entry.value.questionBrief?.isNotEmpty == true) {
//                       final index = entry.key;
//                       final question = entry.value;
//                       final isLastQuestion =
//                           index == form.questions!.length - 1;
//                       return Padding(
//                         padding: EdgeInsets.only(
//                             top: priorityNumber ==
//                                     (widget.task.forms?.length ?? 0)
//                                 ? 12.0
//                                 : 0),
//                         child: _buildPriorityQuestionItem(
//                             context, form, question, isLastQuestion),
//                       );
//                     } else {
//                       return Container();
//                     }
//                   },
//                 ),
//                 (visibleQuestions == null || visibleQuestions.isEmpty)
//                     ? Padding(
//                         padding: EdgeInsets.only(
//                             top: priorityNumber ==
//                                     (widget.task.forms?.length ?? 0)
//                                 ? 12.0
//                                 : 0),
//                         child: Text(
//                           'N/A',
//                           style: textTheme.bodySmall?.copyWith(
//                             fontWeight: FontWeight.w500,
//                             color: AppColors.black,
//                           ),
//                         ),
//                       )
//                     : Container(),
//               ],
//             ),
//           ],
//         ),
//       ],
//     );
//   }

//   // Build individual question item within expanded priority form
//   Widget _buildPriorityQuestionItem(BuildContext context, entities.Form form,
//       entities.Question question, bool isLastQuestion) {
//     final textTheme = Theme.of(context).textTheme;

//     // Find the corresponding answer for this question
//     entities.QuestionAnswer? answer;
//     if (form.questionAnswers != null) {
//       try {
//         answer = form.questionAnswers!.firstWhere(
//           (qa) => qa.questionId == question.questionId,
//         );
//       } catch (e) {
//         answer = null;
//       }
//     }

//     return Container(
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: isLastQuestion
//             ? const BorderRadius.only(
//                 bottomLeft: Radius.circular(10),
//                 bottomRight: Radius.circular(10),
//               )
//             : null,
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           if (question.questionBrief?.isNotEmpty == true)
//             Padding(
//               padding: const EdgeInsets.all(2.0),
//               child: SizedBox(
//                 width: MediaQuery.of(context).size.width - 100,
//                 child: RichText(
//                   maxLines: 2,
//                   overflow: TextOverflow.ellipsis,
//                   text: TextSpan(
//                     text: question.questionBrief ?? 'No question brief',
//                     style: textTheme.bodySmall?.copyWith(
//                       fontWeight: FontWeight.w500,
//                       color: AppColors.black,
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//         ],
//       ),
//     );
//   }

//   // Build document name item for Supporting section with expandable files
//   Widget _buildDocumentNameItem(
//       BuildContext context, entities.Document document) {
//     final textTheme = Theme.of(context).textTheme;
//     final isExpanded =
//         expandedSupportingDocuments.contains(document.documentId);

//     return Container(
//       margin: const EdgeInsets.only(bottom: 12.0),
//       decoration: BoxDecoration(
//         border: Border.all(color: Colors.grey.shade300),
//         borderRadius: BorderRadius.circular(12),
//       ),
//       child: Column(
//         children: [
//           // Document name header - tappable to expand/collapse
//           ListTile(
//             leading: Container(
//               width: 40,
//               height: 40,
//               alignment: Alignment.centerLeft,
//               child: document.documentIconLink != null
//                   ? Image.network(
//                       document.documentIconLink!,
//                       width: 40,
//                       height: 40,
//                       fit: BoxFit.cover,
//                       color: Colors.black,
//                     )
//                   : Image.asset(
//                       AppAssets.taskReport,
//                       width: 40,
//                       height: 40,
//                       scale: 3,
//                       color: Colors.black,
//                     ),
//             ),
//             contentPadding:
//                 const EdgeInsets.symmetric(horizontal: 16.0, vertical: 2.0),
//             title: Text(
//               document.documentName ?? 'Unnamed Document',
//               style: textTheme.montserratTitleExtraSmall.copyWith(
//                 color: Colors.black,
//               ),
//             ),
//             trailing: Icon(
//               isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
//               color: AppColors.black,
//               size: 20,
//             ),
//             onTap: () {
//               setState(() {
//                 if (isExpanded) {
//                   expandedSupportingDocuments.remove(document.documentId);
//                 } else {
//                   expandedSupportingDocuments.add(document.documentId);
//                 }
//               });
//             },
//           ),

//           // Files list - only show when expanded
//           if (isExpanded) ...[
//             if (document.files == null || document.files!.isEmpty)
//               Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Text(
//                   'No files available for this document',
//                   style: textTheme.bodySmall?.copyWith(
//                     color: AppColors.black.withValues(alpha: 0.6),
//                   ),
//                 ),
//               )
//             else
//               ...document.files!.map((file) {
//                 return _buildDocumentFileItem(context, file, document);
//               }),
//             const Gap(12),
//           ],
//         ],
//       ),
//     );
//   }

//   // Build individual file item within expanded document
//   Widget _buildDocumentFileItem(BuildContext context, entities.FileElement file,
//       entities.Document document) {
//     final textTheme = Theme.of(context).textTheme;

//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
//       decoration: BoxDecoration(
//         color: Colors.grey.shade50,
//         borderRadius: BorderRadius.circular(6),
//         border: Border.all(color: Colors.grey.shade200),
//       ),
//       child: ListTile(
//         dense: true,
//         leading: Icon(
//           _getFileIcon(file.documentFileLink),
//           color: AppColors.black,
//           size: 20,
//         ),
//         title: Text(
//           (file.documentFileLink ?? 'Unnamed File').trim().split('\n').last,
//           style: textTheme.bodySmall?.copyWith(
//             color: Colors.black,
//             fontWeight: FontWeight.w500,
//           ),
//           maxLines: 1,
//           overflow: TextOverflow.ellipsis,
//         ),
//         trailing: const Row(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             // Download button
//             // IconButton(
//             //   icon: const Icon(Icons.download, size: 18),
//             //   color: AppColors.black,
//             //   onPressed: () {
//             //     if (file.documentFileLink != null &&
//             //         file.documentFileLink!.isNotEmpty) {
//             //       _downloadFile(file);
//             //     } else {
//             //       ScaffoldMessenger.of(context).showSnackBar(
//             //         const SnackBar(
//             //           content: Text('File URL not available'),
//             //         ),
//             //       );
//             //     }
//             //   },
//             // ),
//             // Open button
//             // IconButton(
//             //   icon: const Icon(Icons.open_in_new, size: 18),
//             //   color: AppColors.black,
//             //   onPressed: () {
//             //     if (file.documentFileLink != null &&
//             //         file.documentFileLink!.isNotEmpty) {
//             //       // Navigate to WebBrowserPage with the file URL
//             //       context.router.push(WebBrowserRoute(url: file.documentFileLink!));
//             //     } else {
//             //       ScaffoldMessenger.of(context).showSnackBar(
//             //         const SnackBar(
//             //           content: Text('File URL not available'),
//             //         ),
//             //       );
//             //     }
//             //   },
//             // ),
//           ],
//         ),
//         onTap: () {
//           // On tap, open the file in WebBrowserPage
//           if (file.documentFileLink != null &&
//               file.documentFileLink!.isNotEmpty) {
//             context.router.push(WebBrowserRoute(url: file.documentFileLink!));
//           } else {
//             SnackBarService.error(
//               context: context,
//               message: 'File URL not available',
//             );
//           }
//         },
//       ),
//     );
//   }

//   // Helper method to get appropriate icon for file type
//   IconData _getFileIcon(String? fileUrl) {
//     if (fileUrl == null) return Icons.description;

//     final url = fileUrl.toLowerCase();
//     if (url.contains('.pdf')) {
//       return Icons.picture_as_pdf;
//     } else if (url.contains('.jpg') ||
//         url.contains('.jpeg') ||
//         url.contains('.png') ||
//         url.contains('.gif')) {
//       return Icons.image;
//     } else if (url.contains('youtube.com') || url.contains('youtu.be')) {
//       return Icons.play_circle;
//     } else if (url.contains('.txt') ||
//         url.contains('.doc') ||
//         url.contains('.docx')) {
//       return Icons.description;
//     } else {
//       return Icons.insert_drive_file;
//     }
//   }

//   // Helper method to get a user-friendly file name from URL
//   String _getFileName(entities.FileElement file) {
//     if (file.documentFileLink == null) {
//       return file.fileId != null ? 'File ${file.fileId}' : 'Document File';
//     }

//     final url = file.documentFileLink!;

//     // Try to extract filename from URL
//     final uri = Uri.parse(url);
//     final pathSegments = uri.pathSegments;
//     if (pathSegments.isNotEmpty) {
//       final lastSegment = pathSegments.last;
//       if (lastSegment.isNotEmpty) {
//         return lastSegment;
//       }
//     }

//     // If YouTube link, return a friendly name
//     if (url.toLowerCase().contains('youtube.com') ||
//         url.toLowerCase().contains('youtu.be')) {
//       return 'YouTube Video';
//     }

//     // Fallback to default name
//     return file.fileId != null ? 'File ${file.fileId}' : 'Document File';
//   }

//   // Download file to device with progress indication
//   Future<void> _downloadFile(entities.FileElement file) async {
//     if (file.documentFileLink == null) {
//       SnackBarService.error(
//         context: context,
//         message: 'File URL not available',
//       );
//       return;
//     }

//     try {
//       // Create a progress value notifier to track download progress
//       ValueNotifier<double> progressNotifier = ValueNotifier<double>(0.0);

//       // Create a cancellation token for the download
//       CancelToken cancelToken = CancelToken();

//       // Show loading indicator with progress and cancel button
//       showDialog(
//         context: context,
//         barrierDismissible: false,
//         builder: (BuildContext context) {
//           return Dialog(
//             child: Padding(
//               padding: const EdgeInsets.all(20.0),
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   ValueListenableBuilder<double>(
//                     valueListenable: progressNotifier,
//                     builder: (context, progress, _) {
//                       return Column(
//                         children: [
//                           LinearProgressIndicator(
//                             value: progress,
//                             backgroundColor: Colors.grey[300],
//                             valueColor: const AlwaysStoppedAnimation<Color>(
//                                 AppColors.primaryBlue),
//                           ),
//                           const SizedBox(height: 16),
//                           Text(
//                             'Downloading file... ${(progress * 100).toInt()}%',
//                             style: const TextStyle(fontWeight: FontWeight.bold),
//                           ),
//                         ],
//                       );
//                     },
//                   ),
//                   const SizedBox(height: 16),
//                   TextButton(
//                     onPressed: () {
//                       // Cancel the download
//                       cancelToken.cancel('Download canceled by user');
//                       Navigator.of(context, rootNavigator: true).pop();
//                     },
//                     child: const Text('Cancel'),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         },
//       );

//       // Get file name
//       final fileName = _getFileName(file);

//       // Get download directory
//       final directory = await getApplicationDocumentsDirectory();
//       final filePath = '${directory.path}/$fileName';

//       // Create Dio instance
//       final dio = Dio();

//       // Download file with progress updates and cancellation support
//       await dio.download(
//         file.documentFileLink!,
//         filePath,
//         cancelToken: cancelToken,
//         onReceiveProgress: (received, total) {
//           if (total != -1) {
//             // Update progress value (between 0.0 and 1.0)
//             final progress = received / total;
//             progressNotifier.value = progress;
//           }
//         },
//       );

//       // Check if widget is still mounted before using context
//       if (!mounted) return;

//       // Close loading dialog
//       Navigator.of(context, rootNavigator: true).pop();

//       // Show success message with more user-friendly information
//       SnackBarService.success(
//         context: context,
//         message: 'File downloaded successfully',
//       );
//     } catch (e) {
//       // Check if widget is still mounted before using context
//       if (!mounted) return;

//       // Close loading dialog if it's still open
//       Navigator.of(context, rootNavigator: true).pop();

//       // Create a more user-friendly error message
//       String errorMessage = 'Failed to download file';

//       // Add more specific error details based on exception type
//       if (e is DioException) {
//         if (e.type == DioExceptionType.cancel) {
//           // Download was canceled by user, no need to show error
//           return;
//         }

//         switch (e.type) {
//           case DioExceptionType.connectionTimeout:
//           case DioExceptionType.sendTimeout:
//           case DioExceptionType.receiveTimeout:
//             errorMessage = 'Connection timed out. Please try again.';
//             break;
//           case DioExceptionType.connectionError:
//             errorMessage = 'No internet connection. Please check your network.';
//             break;
//           case DioExceptionType.badResponse:
//             errorMessage =
//                 'Server error (${e.response?.statusCode}). Please try again later.';
//             break;
//           default:
//             errorMessage = 'Download failed: ${e.message}';
//             break;
//         }
//       } else if (e is FileSystemException) {
//         errorMessage = 'Storage error: Unable to save file.';
//       }

//       // Show error message
//       SnackBarService.error(
//         context: context,
//         message: errorMessage,
//       );
//     }
//   }
// }

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/date_time_utils.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/segment_indicator.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/simple_notification_card.dart';
import 'package:storetrack_app/features/notification/presentation/widgets/notification_card.dart';

import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../widgets/date_converter.dart';

@RoutePage()
class TaskDetailsPage extends StatefulWidget {
  final entities.TaskDetail task;

  const TaskDetailsPage({
    super.key,
    required this.task,
  });

  @override
  State<TaskDetailsPage> createState() => _TaskDetailsPageState();
}

class _TaskDetailsPageState extends State<TaskDetailsPage> {
  int completedFormAmount = 0;
  int maxFormAmount = 0;

  bool showAlert = false;
  bool showDocuments = false;

  Set<num?> expandedDocuments = {};
  Set<num?> expandedSupportingDocuments = {};
  Set<int> expandedForms = {};
  Set<String> expandedDocumentSections = {};

  String? currentDocumentUrl;
  bool showWebView = false;
  WebViewController? webViewController;
  bool isWebViewLoading = true;

  bool showFormQuestions = false;
  entities.Document? currentFormDocument;

  bool isViewingBrief = false;
  bool isOverviewExpanded = false;
  bool isPrioritiesExpanded = false;

  Map<String, List<entities.Taskalert>> groupedTaskAlerts = {};

  @override
  void initState() {
    super.initState();

    if (widget.task.forms != null) {
      for (var formModel in widget.task.forms!) {
        if (formModel.isVisionForm == true) {
          maxFormAmount++;
          if (formModel.isMandatory == true ||
              formModel.formCompleted == true) {
            completedFormAmount++;
          }
        }
      }
    }
  }

  void _toggleMainView(String view) {
    setState(() {
      if (view == 'alert') {
        showAlert = !showAlert;
        if (showAlert) showDocuments = false;
      } else if (view == 'documents') {
        showDocuments = !showDocuments;
        if (showDocuments) showAlert = false;
      }

      isViewingBrief = false;
      isOverviewExpanded = false;
      isPrioritiesExpanded = false;
      showFormQuestions = false;
      showWebView = false;
      currentDocumentUrl = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: (showAlert) ? Colors.white : AppColors.lightGrey2,
      appBar: _buildAppBar(context),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStoreInfoCard(context, widget.task),
            showAlert
                ? Container(
                    color: Colors.white,
                    child: const Divider(),
                  )
                : const Gap(16),
            _buildPageContent(context),
          ],
        ),
      ),
    );
  }

  CustomAppBar _buildAppBar(BuildContext context) {
    return CustomAppBar(
      title: 'Task details',
      actions: [
        GestureDetector(
          child: Image.asset(
            AppAssets.alertIcon,
            scale: 4,
            color: showAlert ? AppColors.primaryBlue : AppColors.black,
          ),
          onTap: () => _toggleMainView('alert'),
        ),
        const Gap(18),
        GestureDetector(
          child: Image.asset(
            AppAssets.documentsIcon,
            color: showDocuments ? AppColors.primaryBlue : AppColors.black,
            scale: 4,
          ),
          onTap: () => _toggleMainView('documents'),
        ),
        const Gap(8),
        _buildPopupMenu(context),
      ],
    );
  }

  PopupMenuButton<String> _buildPopupMenu(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Image.asset(
        AppAssets.taskMore,
        scale: 4,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 8,
      offset: const Offset(0, 20),
      color: Colors.white,
      position: PopupMenuPosition.under,
      constraints: const BoxConstraints(
        minWidth: 240,
        maxWidth: 320,
      ),
      itemBuilder: (context) => [
        _buildPopupMenuItem(context, 'Forms', AppAssets.taskForm),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(context, 'POS', AppAssets.posIcon),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(context, 'Note', AppAssets.alertMessage),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(context, 'Directions', AppAssets.appbarMap),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(context, 'Store info', AppAssets.taskStore),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(
            context, 'Store history', AppAssets.taskStoryHistory),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(
            context, 'Task assistance', AppAssets.taskAssistant),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(context, 'Complete task', AppAssets.taskComplete,
            isBlue: true),
      ],
      onSelected: (value) => _handleMenuSelection(context, value),
    );
  }

  Widget _buildPageContent(BuildContext context) {
    if (showAlert) {
      return _buildAlertsView();
    }
    if (showDocuments) {
      return _buildDocumentsView(context);
    }
    return _buildDefaultView(context);
  }

  Widget _buildDefaultView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildProgressCards(
            context, widget.task, maxFormAmount, completedFormAmount),
        const Gap(16),
        const Divider(color: AppColors.midGrey),
        const Gap(8),
        _buildFormTabs(context, widget.task),
        const Gap(16),
        widget.task.forms?.isEmpty ?? true
            ? const EmptyState(message: 'No forms available')
            : _buildPosReceivedProgress(context),
        const Gap(16),
        const Divider(color: AppColors.midGrey),
        _buildOverviewTabs(context),
        const Gap(16),
      ],
    );
  }

  Widget _buildAlertsView() {
    return Column(
      children: [
        _buildTaskAlertsSection(context),
        const Gap(16),
      ],
    );
  }

  Widget _buildDocumentsView(BuildContext context) {
    return Column(
      children: [
        _buildTaskDocumentSection(context),
        const Gap(16),
      ],
    );
  }

  Future<void> _openGoogleMaps(entities.TaskDetail task) async {
    try {
      double? latitude;
      double? longitude;

      if (task.taskLatitude != null && task.taskLongitude != null) {
        latitude = task.taskLatitude!.toDouble();
        longitude = task.taskLongitude!.toDouble();
      } else if (task.latitude != null && task.longitude != null) {
        latitude = task.latitude!.toDouble();
        longitude = task.longitude!.toDouble();
      }

      if (latitude == null || longitude == null) {
        if (mounted) {
          SnackBarService.warning(
            context: context,
            message: 'Location coordinates not available for this task.',
          );
        }
        return;
      }

      final googleMapsUrl =
          'https://www.google.com/maps?q=$latitude,$longitude';

      context.router.push(WebBrowserRoute(url: googleMapsUrl));
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error opening map: ${e.toString()}',
        );
      }
    }
  }

  Widget _buildStoreInfoCard(
    BuildContext context,
    entities.TaskDetail task,
  ) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(0),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Icon(
                Icons.location_on_outlined,
                size: 16,
                color: AppColors.black,
              ),
              const Gap(8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      task.location ?? "",
                      style: textTheme.montserratTableSmall.copyWith(
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 2,
                    ),
                    const Gap(4),
                    Text(
                      task.storeName ?? "",
                      style: textTheme.titleSmall?.copyWith(
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 1,
                    ),
                    const Gap(4),
                    Text(
                      task.storeGroup ?? "",
                      style: textTheme.montserratTableSmall.copyWith(
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              task.clientLogoUrl?.isEmpty == true
                  ? Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(32),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: const Center(child: Text('N/A')),
                    )
                  : Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                            image: NetworkImage(task.clientLogoUrl ?? '')),
                        borderRadius: BorderRadius.circular(32),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                    ),
            ],
          ),
          const Gap(8),
          const Divider(),
          const Gap(8),
          Row(
            children: [
              const Icon(
                Icons.access_time,
                size: 16,
                color: AppColors.black,
              ),
              const Gap(8),
              Text(
                task.budget != null ? '${task.budget}m' : '',
                style: textTheme.montserratTableSmall
                    .copyWith(fontWeight: FontWeight.w500, fontSize: 12),
              ),
              const Gap(16),
              Image.asset(
                AppAssets.appbarCalendar,
                scale: 6,
                color: Colors.black,
              ),
              const Gap(8),
              Text(
                task.scheduledTimeStamp != null
                    ? convertToDateFormat(task.scheduledTimeStamp.toString())
                    : '',
                style: textTheme.bodySmall,
              ),
              const Spacer(),
              Text(
                'ID: ${task.taskId?.toString() ?? ''}',
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.black,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCards(
    BuildContext context,
    entities.TaskDetail task,
    int max,
    int completed,
  ) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 22),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text('Task Completion',
                      style: textTheme.montserratTitleXxsmall),
                  const Gap(8),
                  Row(
                    children: [
                      Text(
                        (completed == 0 && max == 0)
                            ? '0%'
                            : '${((completed / max) * 100).toStringAsFixed(0)}%',
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const Gap(8),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      final progressWidth = constraints.maxWidth;
                      final adjustedPosition = (completed == 0 && max == 0)
                          ? 0.0
                          : (completed / max) - 0.75;

                      return Stack(
                        clipBehavior: Clip.none,
                        children: [
                          SegmentedProgressIndicator(
                            progress: (completed == 0 && max == 0)
                                ? 0.0
                                : (completed / max),
                            totalWidth: progressWidth,
                            activeColor: AppColors.primaryBlue,
                            backgroundColor: Colors.grey.shade200,
                            dividerColor: Colors.black,
                            height: 10,
                            segments: 10,
                            borderRadius: 10,
                          ),
                          Positioned(
                            left: adjustedPosition,
                            top: -8,
                            bottom: 0,
                            child: Container(
                              width: 1.5,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  const Gap(8),
                  Text(
                      '${task.ctFormsCompletedCnt ?? 0} of ${task.ctFormsTotalCnt ?? 0} forms',
                      style: textTheme.montserratTableSmall),
                ],
              ),
            ),
          ),
          const Gap(16),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('POS Received', style: textTheme.montserratTitleXxsmall),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppAssets.dashboardPos,
                        width: 28,
                      ),
                      const Gap(8),
                      Text(
                        task.posReceived != null
                            ? task.posReceived.toString()
                            : '0',
                        style: textTheme.titleSmall?.copyWith(
                          fontSize: 40,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Center(
                        child: SizedBox(
                          height: 30,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                ' of ${task.posItems != null ? task.posItems!.length : '0'}',
                                style: textTheme.bodySmall?.copyWith(
                                  color: AppColors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Text(
                    'Delivered',
                    style: textTheme.montserratTableSmall.copyWith(
                        color: AppColors.primaryBlue,
                        fontStyle: FontStyle.italic),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormTabs(BuildContext context, entities.TaskDetail task) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16.0, right: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Form queue',
                style: textTheme.montserratTitleSmall,
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              const Icon(
                Icons.check_box_outlined,
                size: 16,
              ),
              const Gap(4),
              Text('Forms', style: textTheme.montserratTitleExtraSmall),
              const Gap(16),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPosReceivedProgress(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final List progressItems = widget.task.forms ?? [];

    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        itemCount: progressItems.length,
        itemBuilder: (context, index) {
          return Container(
            width: MediaQuery.of(context).size.width * 0.8,
            margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  progressItems[index].formName ?? '',
                  style: textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const Gap(12),
                Row(
                  children: [
                    Expanded(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: LinearProgressIndicator(
                          value: 0,
                          backgroundColor: Colors.grey.shade200,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                              AppColors.primaryBlue),
                          minHeight: 8,
                        ),
                      ),
                    ),
                    const Gap(24),
                    Text(
                      '0 of 0',
                      style: textTheme.montserratTableSmall.copyWith(
                        color: AppColors.black.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildOverviewTabs(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Overview',
                style: textTheme.montserratTitleSmall,
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              Image.asset(
                AppAssets.documentsIcon,
                color: AppColors.black,
                scale: 5,
              ),
              const Gap(4),
              Text('Documents',
                  style: textTheme.montserratTitleExtraSmall.copyWith(
                    color: AppColors.black,
                  )),
              const Gap(8),
            ],
          ),
        ),
        _buildOverviewContent(context),
      ],
    );
  }

  Widget _buildOverviewContent(BuildContext context) {
    _groupTaskAlertsByDate();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.task.taskalerts != null &&
                  widget.task.taskalerts!.isNotEmpty) ...[
                Builder(builder: (context) {
                  final latestEntry = groupedTaskAlerts.entries.first;
                  final latestAlert = latestEntry.value.first;
                  return SimpleNotificationCard(
                    type: SimpleNotificationType.urgent,
                    message: latestAlert.message ?? 'No message',
                  );
                }),
              ] else ...[
                const SimpleNotificationCard(
                  type: SimpleNotificationType.urgent,
                  message: 'No alerts available',
                )
              ],
              const Gap(16),
              SimpleNotificationCard(
                type: SimpleNotificationType.manager,
                message: widget.task.taskNote!.isNotEmpty
                    ? widget.task.taskNote!
                    : 'No notes available',
              ),
              const Gap(16),
              const SimpleNotificationCard(
                type: SimpleNotificationType.info,
                title: 'Brief summary',
                message: 'The summary of the brief will be shown here',
              ),
            ],
          ),
        ),
      ),
    );
  }

  PopupMenuItem<String> _buildPopupMenuItem(
      BuildContext context, String title, String icon,
      {bool isBlue = false}) {
    return PopupMenuItem<String>(
      value: title,
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                  color: isBlue ? AppColors.primaryBlue : Colors.black,
                ),
          ),
          const SizedBox(width: 16),
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Image.asset(
                icon,
                scale: 3,
                color: Colors.black,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _groupTaskAlertsByDate() {
    groupedTaskAlerts.clear();
    for (var alert in widget.task.taskalerts ?? []) {
      final index = widget.task.taskalerts!.indexOf(alert);
      final daysAgo = index % 3;
      final fakeDate = DateTime.now().subtract(Duration(days: daysAgo));
      final formattedDate = DateFormat('EEE dd MMM').format(fakeDate);

      if (!groupedTaskAlerts.containsKey(formattedDate)) {
        groupedTaskAlerts[formattedDate] = [];
      }
      groupedTaskAlerts[formattedDate]!.add(alert);
    }
  }

  Widget _buildTaskAlertsSection(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    _groupTaskAlertsByDate();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(0),
      child: (widget.task.taskalerts == null || widget.task.taskalerts!.isEmpty)
          ? const EmptyState(message: 'No task alerts available')
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...groupedTaskAlerts.entries.map((entry) {
                  final date = entry.key;
                  final alertsForThisDate = entry.value;
                  final isToday =
                      date == DateFormat('EEE dd MMM').format(DateTime.now());

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                        child: Text(
                          date,
                          style: textTheme.titleSmall?.copyWith(
                            color: isToday
                                ? AppColors.primaryBlue
                                : AppColors.black,
                          ),
                        ),
                      ),
                      ...alertsForThisDate.map((alert) {
                        NotificationType notificationType =
                            NotificationType.message;
                        if (alert.subject
                                    ?.toLowerCase()
                                    .contains('urgent') ==
                                true ||
                            alert.message?.toLowerCase().contains('urgent') ==
                                true ||
                            alert.message?.toLowerCase().contains('asap') ==
                                true) {
                          notificationType = NotificationType.urgent;
                        }

                        final index = alertsForThisDate.indexOf(alert);
                        final hoursAgo = 1 + (index * 2);
                        final fakeDate =
                            DateTime.now().subtract(Duration(hours: hoursAgo));
                        final timeAgo = getTimeAgo(fakeDate);

                        return NotificationCard(
                          type: notificationType,
                          message: alert.message ?? 'No message',
                          company: widget.task.client ?? 'Unknown',
                          task: alert.subject ?? 'No subject',
                          location: widget.task.location ?? 'No location',
                          timeAgo: timeAgo,
                          duration: widget.task.budget != null
                              ? '${widget.task.budget}m'
                              : '0m',
                        );
                      }),
                      if (entry.key != groupedTaskAlerts.keys.last)
                        Padding(
                          padding:
                              const EdgeInsets.only(top: 8.0, bottom: 16.0),
                          child: Divider(
                            height: 1,
                            thickness: 1,
                            color: AppColors.appBarBorderBlack,
                          ),
                        ),
                    ],
                  );
                }),
              ],
            ),
    );
  }

  Widget _buildTaskDocumentSection(BuildContext context) {
    if (isViewingBrief) {
      return _buildTaskBriefSection(context);
    }
    if (showWebView && currentDocumentUrl != null) {
      return _buildDocumentWebView(context);
    }
    if (showFormQuestions) {
      return _buildFormQuestionsView(context);
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.task.documents == null
              ? const Padding(
                  padding: EdgeInsets.all(0),
                  child: EmptyState(message: 'No documents available'),
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 20, left: 16, right: 16, bottom: 8),
                      child: _buildDocumentCategory(true, context, 'Main', []),
                    ),
                    Divider(
                      color: AppColors.black10,
                      height: 1,
                      thickness: 1,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          bottom: 12, left: 16, right: 16, top: 20),
                      child: _buildDocumentCategory(
                        false,
                        context,
                        'Supporting',
                        widget.task.documents!.toList(),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildDocumentWebView(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    showWebView = false;
                    currentDocumentUrl = null;
                    webViewController = null;
                  });
                },
              ),
              Text(
                'Document Viewer',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  webViewController?.reload();
                },
              ),
            ],
          ),
          Container(
            height: MediaQuery.of(context).size.height * 0.6,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(10),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Stack(
                children: [
                  WebViewWidget(
                    controller: webViewController ?? _initWebViewController(),
                  ),
                  if (isWebViewLoading)
                    const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.primaryBlue,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormQuestionsView(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    showFormQuestions = false;
                    currentFormDocument = null;
                  });
                },
              ),
              const SizedBox(width: 48),
            ],
          ),
          Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            child: SingleChildScrollView(
              child: _buildFormQuestionsContent(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormQuestionsContent(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    if (widget.task.forms == null || widget.task.forms!.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'No form questions available for this task.',
          style: textTheme.bodyMedium?.copyWith(
            color: AppColors.black.withOpacity(0.6),
          ),
        ),
      );
    }

    List<Widget> formWidgets = [];
    for (var form in widget.task.forms!) {
      if (form.questions != null && form.questions!.isNotEmpty) {
        formWidgets.add(_buildFormSection(context, form));
        formWidgets.add(const SizedBox(height: 16));
      }
    }

    if (formWidgets.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'No questions found in the forms.',
          style: textTheme.bodyMedium?.copyWith(
            color: AppColors.black.withOpacity(0.6),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: formWidgets,
    );
  }

  Widget _buildFormSection(BuildContext context, entities.Form form) {
    final textTheme = Theme.of(context).textTheme;
    final formId = (form.formId ?? form.hashCode).toInt();
    final isExpanded = expandedForms.contains(formId);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              if (isExpanded) {
                expandedForms.remove(formId);
              } else {
                expandedForms.add(formId);
              }
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: isExpanded
                  ? const BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                    )
                  : BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    form.formName ?? 'Unnamed Form',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_right,
                  color: AppColors.black,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        if (isExpanded) ...[
          ...form.questions!.asMap().entries.map((entry) {
            final index = entry.key;
            final question = entry.value;
            final isLastQuestion = index == form.questions!.length - 1;
            return _buildQuestionAnswerItem(
                context, form, question, isLastQuestion);
          }),
        ],
      ],
    );
  }

  Widget _buildQuestionAnswerItem(
      BuildContext context, entities.Form form, entities.Question question,
      [bool isLastQuestion = false]) {
    final textTheme = Theme.of(context).textTheme;

    entities.QuestionAnswer? answer;
    if (form.questionAnswers != null) {
      try {
        answer = form.questionAnswers!.firstWhere(
          (qa) => qa.questionId == question.questionId,
        );
      } catch (e) {
        answer = null;
      }
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: isLastQuestion
            ? const BorderRadius.only(
                bottomLeft: Radius.circular(10),
                bottomRight: Radius.circular(10),
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question.questionBrief ?? 'No question description',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getAnswerText(answer),
            style: textTheme.bodyMedium?.copyWith(
              color: AppColors.black.withOpacity(0.8),
            ),
          ),
          if (question.questionBrief?.isNotEmpty == true) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: AppColors.lightGrey2.withOpacity(0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'Brief: ${question.questionBrief}',
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.black.withOpacity(0.7),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _getAnswerText(entities.QuestionAnswer? answer) {
    if (answer == null) {
      return 'No answer ';
    }
    if (answer.measurementTextResult?.isNotEmpty == true) {
      return answer.measurementTextResult!;
    }
    if (answer.measurementOptionId != null) {
      return 'Option ${answer.measurementOptionId}';
    }
    if (answer.measurementOptionIds?.isNotEmpty == true) {
      return 'Options: ${answer.measurementOptionIds}';
    }
    return 'Answer provided (no text available)';
  }

  WebViewController _initWebViewController() {
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              isWebViewLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              isWebViewLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              isWebViewLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(currentDocumentUrl!));

    return webViewController!;
  }

  Widget _buildDocumentCategory(
    bool isMainSection,
    BuildContext context,
    String title,
    List<entities.Document> documents,
  ) {
    final textTheme = Theme.of(context).textTheme;
    final isExpanded = expandedDocumentSections.contains(title);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              if (isExpanded) {
                expandedDocumentSections.remove(title);
              } else {
                expandedDocumentSections.add(title);
              }
            });
          },
          child: Container(
            color: Colors.transparent,
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 0.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(title, style: textTheme.montserratTitleSmall),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_right,
                  color: AppColors.black,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),
        if (isExpanded) ...[
          if (isMainSection)
            _buildBriefItem(context)
          else
            documents.isEmpty
                ? EmptyState(
                    message: 'No ${title.toLowerCase()} documents available',
                    margin: const EdgeInsets.symmetric(vertical: 8),
                  )
                : Column(
                    children: documents.map((document) {
                      return _buildDocumentNameItem(context, document);
                    }).toList(),
                  ),
        ],
      ],
    );
  }

  Widget _buildBriefItem(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(10),
      ),
      child: ListTile(
        leading: Padding(
          padding: const EdgeInsets.only(left: 14.0),
          child: Image.asset(
            AppAssets.supportDocument,
            scale: 4,
            color: Colors.black,
          ),
        ),
        title: Text(
          'Brief',
          style: textTheme.montserratTitleExtraSmall.copyWith(
            color: Colors.black,
          ),
        ),
        onTap: () {
          setState(() {
            isViewingBrief = true;
          });
        },
      ),
    );
  }

  Widget _buildTaskBriefSection(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    setState(() {
                      isViewingBrief = false;
                    });
                  },
                ),
                Expanded(
                  child: Text(
                    'Brief',
                    style: textTheme.titleMedium
                        ?.copyWith(fontWeight: FontWeight.w600),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 48),
              ],
            ),
          ),
          const Divider(height: 1),
          ListTile(
            title: Text(
              'Overview',
              style: textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
            trailing: Icon(
              isOverviewExpanded
                  ? Icons.keyboard_arrow_up
                  : Icons.keyboard_arrow_right,
              color: AppColors.black,
              size: 20,
            ),
            onTap: () {
              setState(() {
                isOverviewExpanded = !isOverviewExpanded;
              });
            },
          ),
          if (isOverviewExpanded)
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: _buildOverviewTabContent(context),
            ),
          ListTile(
            title: Text(
              'Brief',
              style: textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
            trailing: Icon(
              isPrioritiesExpanded
                  ? Icons.keyboard_arrow_up
                  : Icons.keyboard_arrow_right,
              color: AppColors.black,
              size: 20,
            ),
            onTap: () {
              setState(() {
                isPrioritiesExpanded = !isPrioritiesExpanded;
              });
            },
          ),
          if (isPrioritiesExpanded)
            Container(
              padding: const EdgeInsets.all(16.0),
              child: _buildPrioritiesTabContent(context),
            ),
        ],
      ),
    );
  }

  Widget _buildOverviewTabContent(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Exciting Task Alert!',
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.black,
                ),
              ),
              const Gap(12),
              Text(
                "The summary of the task brief goes here. \n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl nec ultricies tincidunt, nunc nisl tincidunt nunc, nec tincidunt nisl nisl nec nisl. \n\nNullam euismod, nisl nec ultricies tincidunt, nunc nisl tincidunt nunc, nec tincidunt nisl nisl nec nisl.",
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.black.withValues(alpha: 0.8),
                  height: 1.4,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrioritiesTabContent(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.task.forms != null && widget.task.forms!.isNotEmpty)
          ...widget.task.forms!.asMap().entries.map((entry) {
            final index = entry.key;
            final form = entry.value;

            return _buildPriorityFormItem(context, index + 1, form);
          })
        else
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Text(
              'No priority forms available',
              style: textTheme.bodySmall?.copyWith(
                color: AppColors.black.withValues(alpha: 0.6),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPriorityFormItem(
    BuildContext context,
    int priorityNumber,
    entities.Form form,
  ) {
    final textTheme = Theme.of(context).textTheme;
    final visibleQuestions = form.questions
        ?.where((q) => q.questionBrief?.isNotEmpty == true)
        .toList();
    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.black20),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      priorityNumber.toString(),
                      style: textTheme.bodySmall?.copyWith(
                        color: AppColors.primaryBlue,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                const Gap(12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      form.formName ?? 'Unnamed Form',
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.black,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Row(
              children: [
                if (!(form.questions == null || form.questions!.isEmpty))
                  Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    child: Container(
                      width: 2,
                      height: 60,
                      color: AppColors.black10,
                      margin: const EdgeInsets.only(top: 0),
                    ),
                  ),
                priorityNumber == (widget.task.forms?.length ?? 0)
                    ? const Gap(36)
                    : const Gap(26),
                ...form.questions!.asMap().entries.map(
                  (entry) {
                    if (entry.value.questionBrief?.isNotEmpty == true) {
                      final index = entry.key;
                      final question = entry.value;
                      final isLastQuestion =
                          index == form.questions!.length - 1;
                      return Padding(
                        padding: EdgeInsets.only(
                            top: priorityNumber ==
                                    (widget.task.forms?.length ?? 0)
                                ? 12.0
                                : 0),
                        child: _buildPriorityQuestionItem(
                            context, form, question, isLastQuestion),
                      );
                    } else {
                      return Container();
                    }
                  },
                ),
                (visibleQuestions == null || visibleQuestions.isEmpty)
                    ? Padding(
                        padding: EdgeInsets.only(
                            top: priorityNumber ==
                                    (widget.task.forms?.length ?? 0)
                                ? 12.0
                                : 0),
                        child: Text(
                          'N/A',
                          style: textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: AppColors.black,
                          ),
                        ),
                      )
                    : Container(),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPriorityQuestionItem(BuildContext context, entities.Form form,
      entities.Question question, bool isLastQuestion) {
    final textTheme = Theme.of(context).textTheme;

    entities.QuestionAnswer? answer;
    if (form.questionAnswers != null) {
      try {
        answer = form.questionAnswers!.firstWhere(
          (qa) => qa.questionId == question.questionId,
        );
      } catch (e) {
        answer = null;
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: isLastQuestion
            ? const BorderRadius.only(
                bottomLeft: Radius.circular(10),
                bottomRight: Radius.circular(10),
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (question.questionBrief?.isNotEmpty == true)
            Padding(
              padding: const EdgeInsets.all(2.0),
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 100,
                child: RichText(
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  text: TextSpan(
                    text: question.questionBrief ?? 'No question brief',
                    style: textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDocumentNameItem(
      BuildContext context, entities.Document document) {
    final textTheme = Theme.of(context).textTheme;
    final isExpanded =
        expandedSupportingDocuments.contains(document.documentId);

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              width: 40,
              height: 40,
              alignment: Alignment.centerLeft,
              child: document.documentIconLink != null
                  ? Image.network(
                      document.documentIconLink!,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                      color: Colors.black,
                    )
                  : Image.asset(
                      AppAssets.taskReport,
                      width: 40,
                      height: 40,
                      scale: 3,
                      color: Colors.black,
                    ),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 2.0),
            title: Text(
              document.documentName ?? 'Unnamed Document',
              style: textTheme.montserratTitleExtraSmall.copyWith(
                color: Colors.black,
              ),
            ),
            trailing: Icon(
              isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              color: AppColors.black,
              size: 20,
            ),
            onTap: () {
              setState(() {
                if (isExpanded) {
                  expandedSupportingDocuments.remove(document.documentId);
                } else {
                  expandedSupportingDocuments.add(document.documentId);
                }
              });
            },
          ),
          if (isExpanded) ...[
            if (document.files == null || document.files!.isEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No files available for this document',
                  style: textTheme.bodySmall?.copyWith(
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
              )
            else
              ...document.files!.map((file) {
                return _buildDocumentFileItem(context, file, document);
              }),
            const Gap(12),
          ],
        ],
      ),
    );
  }

  Widget _buildDocumentFileItem(BuildContext context, entities.FileElement file,
      entities.Document document) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: ListTile(
        dense: true,
        leading: Icon(
          _getFileIcon(file.documentFileLink),
          color: AppColors.black,
          size: 20,
        ),
        title: Text(
          (file.documentFileLink ?? 'Unnamed File').trim().split('\n').last,
          style: textTheme.bodySmall?.copyWith(
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        onTap: () {
          if (file.documentFileLink != null &&
              file.documentFileLink!.isNotEmpty) {
            context.router.push(WebBrowserRoute(url: file.documentFileLink!));
          } else {
            SnackBarService.error(
              context: context,
              message: 'File URL not available',
            );
          }
        },
      ),
    );
  }

  IconData _getFileIcon(String? fileUrl) {
    if (fileUrl == null) return Icons.description;
    final url = fileUrl.toLowerCase();
    if (url.contains('.pdf')) {
      return Icons.picture_as_pdf;
    } else if (url.contains('.jpg') ||
        url.contains('.jpeg') ||
        url.contains('.png') ||
        url.contains('.gif')) {
      return Icons.image;
    } else if (url.contains('youtube.com') || url.contains('youtu.be')) {
      return Icons.play_circle;
    } else if (url.contains('.txt') ||
        url.contains('.doc') ||
        url.contains('.docx')) {
      return Icons.description;
    } else {
      return Icons.insert_drive_file;
    }
  }

  void _handleMenuSelection(BuildContext context, String value) {
    switch (value) {
      case 'Forms':
        context.navigateTo(FormRoute(
          task: widget.task,
        ));
        break;
      case 'POS':
        context.navigateTo(PosRoute(
          task: widget.task,
        ));
        break;
      case 'Note':
        context.navigateTo(NotesRoute(
          task: widget.task,
        ));
        break;
      case 'Directions':
        _openGoogleMaps(widget.task);
        break;
      case 'Store info':
        context.navigateTo(const StoreInfoRoute());
        break;
      case 'Store history':
        context.navigateTo(StoreHistoryRoute(
          storeId: (widget.task.storeId ?? 0).toInt(),
          taskId: (widget.task.taskId ?? 0).toInt(),
        ));
        break;
      case 'Task assistance':
      case 'Complete task':
        break;
    }
  }
}
